<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .page-link {
            display: block;
            padding: 12px 20px;
            margin: 8px 0;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background 0.3s;
        }
        .page-link:hover {
            background: #0056b3;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-left: 10px;
        }
        .status.loading {
            background: #ffc107;
            color: #000;
        }
        .status.success {
            background: #28a745;
            color: white;
        }
        .status.error {
            background: #dc3545;
            color: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>惠他智能 - 页面访问测试</h1>
        <p>点击下面的链接测试各个页面是否能正常访问：</p>
        
        <a href="http://localhost:3000/" class="page-link" target="_blank">
            首页 (Home) <span class="status loading">待测试</span>
        </a>
        
        <a href="http://localhost:3000/about" class="page-link" target="_blank">
            关于我们 (About) <span class="status loading">待测试</span>
        </a>
        
        <a href="http://localhost:3000/cases" class="page-link" target="_blank">
            案例中心 (Cases) <span class="status loading">待测试</span>
        </a>
        
        <a href="http://localhost:3000/partners" class="page-link" target="_blank">
            合作伙伴 (Partners) <span class="status loading">待测试</span>
        </a>
        
        <a href="http://localhost:3000/compare" class="page-link" target="_blank">
            产品对比 (Comparison) <span class="status loading">待测试</span>
        </a>
        
        <a href="http://localhost:3000/privacy" class="page-link" target="_blank">
            隐私政策 (Privacy) <span class="status loading">待测试</span>
        </a>

        <h2>性能优化成果</h2>
        <ul>
            <li>✅ 图片懒加载和WebP支持</li>
            <li>✅ React组件性能优化 (memo, useMemo, useCallback)</li>
            <li>✅ Canvas背景动画优化</li>
            <li>✅ 代码分割和懒加载</li>
            <li>✅ Bundle优化配置</li>
            <li>✅ CSS性能优化</li>
            <li>✅ 动画性能优化</li>
        </ul>

        <h2>技术栈</h2>
        <ul>
            <li>React 18 + TypeScript</li>
            <li>Vite 6 (构建工具)</li>
            <li>Tailwind CSS (样式)</li>
            <li>Framer Motion (动画)</li>
            <li>React Router (路由)</li>
            <li>Lucide React (图标)</li>
        </ul>
    </div>

    <script>
        // 简单的页面状态检测
        document.addEventListener('DOMContentLoaded', function() {
            const links = document.querySelectorAll('.page-link');
            
            links.forEach(link => {
                link.addEventListener('click', function() {
                    const status = this.querySelector('.status');
                    status.textContent = '已打开';
                    status.className = 'status success';
                });
            });
        });
    </script>
</body>
</html>
