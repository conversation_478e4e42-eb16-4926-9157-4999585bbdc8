import { Link } from 'react-router-dom';
import Background from '@/components/Background';
import Footer from '@/components/Footer';

export default function AICallCenter() {
  return (
    <div className="min-h-screen flex flex-col">
      <main className="flex-1 relative">
        <Background />
        <div className="container mx-auto px-4 py-12 relative z-10">
          <div className="max-w-4xl mx-auto bg-white/90 p-6 rounded-xl shadow-md backdrop-blur-sm">
            <h1 className="text-3xl font-bold mb-6 text-blue-600">智能外呼机器人</h1>
            
            <div className="prose max-w-none">
              <h2 className="text-2xl font-semibold mb-4 text-blue-500">产品概述</h2>
              <p className="mb-4">
                我们的智能外呼机器人采用最先进的自然语言处理和情感识别技术，能够实现高效、人性化的债务沟通。
              </p>
              
              <h2 className="text-2xl font-semibold mb-4 text-blue-500">核心功能</h2>
              <ul className="space-y-2 mb-6">
                <li>24/7 全天候工作，提升沟通效率</li>
                <li>智能情绪识别，优化沟通策略</li>
                <li>多语言支持，覆盖更广泛人群</li>
                <li>实时数据分析，优化运营效果</li>
              </ul>
              
              <h2 className="text-2xl font-semibold mb-4 text-blue-500">技术参数</h2>
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <p className="font-medium">识别准确率</p>
                  <p className="text-2xl font-bold text-blue-600">98%</p>
                </div>
                <div className="bg-blue-50 p-4 rounded-lg">
                  <p className="font-medium">响应速度</p>
                  <p className="text-2xl font-bold text-blue-600">0.5s</p>
                </div>
                <div className="bg-blue-50 p-4 rounded-lg">
                  <p className="font-medium">并发量</p>
                  <p className="text-2xl font-bold text-blue-600">1000+</p>
                </div>
                <div className="bg-blue-50 p-4 rounded-lg">
                  <p className="font-medium">识别速度</p>
                  <p className="text-2xl font-bold text-blue-600">200ms</p>
                </div>
              </div>
              
              <div className="mt-8">
                <Link 
                  to="/compare" 
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 inline-flex items-center gap-2"
                >
                  <i class="fa-solid fa-arrow-left"></i> 返回对比页面
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}