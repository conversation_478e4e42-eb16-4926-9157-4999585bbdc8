import { useState } from 'react';
import AIBotComparison from '@/components/AIBotComparison';
import Background from '@/components/Background';
import Footer from '@/components/Footer';
import ComparisonCube from '@/components/ComparisonCube';
import { comparisonData } from '@/data/mock';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

// 不良资产运营系统解决方案数据
const solutionData = [
  {
    title: "多模态交互系统",
    subtitle: "实现信息高效传递与交互",
    icon: "fa-solid fa-comments",
    color: "from-blue-500 to-blue-600",
    features: ["外呼平台", "短信平台", "微信平台"],
    description: "作为系统的'嘴巴'，实现与债务人的多渠道高效沟通"
  },
  {
    title: "资产清收系统", 
    subtitle: "驱动系统流程高效运转",
    icon: "fa-solid fa-gears",
    color: "from-purple-500 to-purple-600",
    features: ["资产处置流程", "实时监控与数据分析", "合规管理"],
    description: "作为系统的'躯干'，确保处置流程高效合规运行"
  },
  {
    title: "大模型工程",
    subtitle: "智能决策，优化交互和策略",
    icon: "fa-solid fa-brain",
    color: "from-green-500 to-green-600",
    features: ["Qwen模型", "ChatGLM模型", "Deepseek R1模型"],
    description: "作为系统的'大脑'，提供智能化决策支持"
  }
];


export default function Comparison() {
  const [activeTab, setActiveTab] = useState('compare');
  
  // 系统设置 - 控制债务和解流程显示
  const systemSettings = {
    showDebtSettlementProcess: false
  };

  const debtSettlementProcess = [
    {
      title: "广告宣传",
      icon: "fa-solid fa-bullhorn",
      content: "回答我们是谁、我们可以帮助你什么、我们已经帮助很多人走出了债务困境",
      color: "from-blue-500 to-blue-600"
    },
    {
      title: "取得和债务人的联系",
      icon: "fa-solid fa-handshake",
      content: "如何使得债务人愿意添加企业微信，例如：加微信领取100元利息抵用券",
      color: "from-purple-500 to-purple-600"
    },
    {
      title: "认筹锁定优惠",
      icon: "fa-solid fa-lock",
      content: "初步测试债务人的还款意愿，例如：优惠政策为交1000元抵2000元，三个月内签署和解协议优惠有效",
      color: "from-green-500 to-green-600"
    },
    {
      title: "交定金锁定折扣优惠",
      icon: "fa-solid fa-percent",
      content: "进一步锁定高还款意愿群体，例如：优惠政策为交欠款本金的10%可以享欠款本金90折优惠，一个月内签署和解协议优惠有效",
      color: "from-yellow-500 to-yellow-600"
    },
    {
      title: "签约确定尾款付款方式",
      icon: "fa-solid fa-file-contract",
      content: "提供多种付款方式配套相应的优惠政策：一次性还款、等额还款、气球式还款（262）、递增式还款，例如：优惠政策为签约就送生活大礼包（米面油）",
      color: "from-red-500 to-red-600"
    },
    {
      title: "结清",
      icon: "fa-solid fa-check-circle",
      content: "鼓励债务人在还款过程中提前结清所有或者部分欠款，例如优惠政策为提前还款，可享8%返还",
      color: "from-indigo-500 to-indigo-600"
    },
    {
      title: "债务人结清后",
      icon: "fa-solid fa-chart-line",
      content: "债务人结清后转为常规营销，维持客户粘性，随着客户生活水平的提升，提供相应的服务",
      color: "from-pink-500 to-pink-600"
    }
  ];

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-blue-900/10 to-purple-900/10">
      <main className="flex-1 relative">
        <Background />
        <div className="container mx-auto px-4 py-12 relative z-10">
          {/* 不良资产运营系统解决方案板块 */}
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="mb-20"
          >
            <h2 className="text-3xl font-bold text-center mb-12 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500 font-['Rajdhani']">
              系统协同运作，形成高效、智能的不良资产处置闭环
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {solutionData.map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ y: -10 }}
                  className={`bg-gradient-to-br ${item.color} backdrop-blur-sm p-8 rounded-2xl shadow-xl border border-white/10 hover:shadow-2xl transition-all`}
                >
                  <div className="flex flex-col items-center text-center">
                    <div className="bg-white/20 p-4 rounded-full mb-6 shadow-inner">
                      <i className={`${item.icon} text-white text-2xl`}></i>
                    </div>
                    <h3 className="text-xl font-semibold text-white mb-2 font-['Rajdhani']">{item.title}</h3>
                    <p className="text-blue-100 mb-4 font-['Inter']">{item.subtitle}</p>
                    <ul className="space-y-2 mb-4 font-['Inter']">
                      {item.features.map((feature, i) => (
                        <li key={i} className="text-gray-200 flex items-center">
                          <i className="fa-solid fa-check mr-2 text-blue-200"></i>
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <p className="text-white/80 text-sm mt-auto font-['Inter']">{item.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.section>

          <motion.h1 
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-4xl font-bold text-center mb-12 bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent"
          >
            产品与服务
          </motion.h1>
          
              {/* 债务和解流程板块 - 科技感设计 (根据系统设置显示/隐藏) */}
              {systemSettings.showDebtSettlementProcess && (
                <motion.section
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5 }}
                  className="mb-20"
                >
                  <h2 className="text-3xl font-bold text-center mb-12 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400 neon-effect font-['Rajdhani']">
                    债务和解流程
                  </h2>
                  
                  <div className="relative">
                    {/* 流程连接线 */}
                    <div className="hidden lg:block absolute top-1/2 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-blue-500 opacity-30 -translate-y-1/2"></div>
                    
                    <div className="grid grid-cols-1 lg:grid-cols-7 gap-4 lg:gap-0">
                      {debtSettlementProcess.map((step, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, y: 20 }}
                          whileInView={{ 
                            opacity: 1, 
                            y: 0,
                            transition: { delay: index * 0.1 }
                          }}
                          viewport={{ once: true, margin: "-50px" }}
                          className="relative z-10"
                        >
                          {/* 流程步骤连接点 */}
                          <div className="hidden lg:flex absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 items-center justify-center w-8 h-8 rounded-full bg-gradient-to-br from-blue-600 to-purple-600 shadow-lg border-2 border-white/20 animate-text-glow">
                            <span className="text-white font-bold font-['Rajdhani']">{index + 1}</span>
                          </div>
                          
                          <motion.div
                            whileHover={{ 
                              y: -10,
                              scale: 1.05,
                              boxShadow: "0 20px 25px -5px rgba(59, 130, 246, 0.3)"
                            }}
                            transition={{ type: "spring", stiffness: 400, damping: 10 }}
                            className={`bg-gradient-to-br ${step.color} backdrop-blur-sm p-6 rounded-2xl shadow-xl border border-white/10 mx-2 lg:mx-0 lg:mt-8 transition-all`}
                          >
                            <div className="flex flex-col items-center text-center">
                              <div className="bg-white/20 p-4 rounded-full mb-4 shadow-inner">
                                <i className={`${step.icon} text-white text-2xl`}></i>
                              </div>
                              <h3 className="text-xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-200 mb-3 font-['Rajdhani']">{step.title}</h3>
                              <p className="text-gray-300 text-sm leading-relaxed font-['Inter']"></p>
                            </div>
                          </motion.div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </motion.section>
              )}
          
          <div className="flex flex-col lg:flex-row gap-12 mb-20">
            <div className="lg:w-2/3">
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
               <h2 className="text-2xl font-semibold mb-6 text-white font-['Rajdhani']">AI智能对比</h2>
                <AIBotComparison data={comparisonData} />
              </motion.div>
            </div>
            
            <motion.div 
              className="lg:w-1/3 bg-white/90 backdrop-blur-sm p-8 rounded-xl shadow-lg border border-white/20"
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <h2 className="text-2xl font-semibold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent font-['Rajdhani']">对比说明</h2>
              <div className="space-y-4 text-gray-700 font-['Inter']">
                <p>通过三维度对比展示惠他智能"普惠化债"创新模式与传统催收方式的差异：</p>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <div className="bg-blue-100 text-blue-600 p-1 rounded-full mr-3 mt-1">
                       <i className="fa-solid fa-microchip"></i>
                    </div>
                    <div>
                      <span className="font-medium">技术维度</span>：AI情绪识别技术提升沟通效率
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="bg-green-100 text-green-600 p-1 rounded-full mr-3 mt-1">
                      <i class="fa-solid fa-users"></i>
                    </div>
                    <div>
                      <span className="font-medium">社会维度</span>：信用修复机制帮助债务人重建信用
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="bg-purple-100 text-purple-600 p-1 rounded-full mr-3 mt-1">
                      <i class="fa-solid fa-chart-line"></i>
                    </div>
                    <div>
                      <span className="font-medium">财务维度</span>：优化清偿方案提高各方收益
                    </div>
                  </li>
                </ul>
              </div>
            </motion.div>
          </div>

          {/* 智能外呼机器人产品模块 - 调整到前面 */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="bg-gradient-to-r from-blue-900/30 to-purple-900/30 backdrop-blur-sm rounded-2xl overflow-hidden shadow-xl border border-white/10 mb-20"
          >
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="p-10 flex flex-col justify-center">
                <h2 className="text-3xl font-bold text-white mb-4 font-['Rajdhani']">智能外呼机器人</h2>
                <p className="text-gray-300 mb-6 font-['Inter']">
                  我们的智能外呼机器人采用最先进的自然语言处理和情感识别技术，能够实现高效、人性化的债务沟通。
                </p>
                <ul className="space-y-3 mb-8 font-['Inter']">
                  <li className="flex items-center text-gray-300">
                    <i class="fa-solid fa-check-circle text-green-400 mr-2"></i>
                    24/7 全天候工作，提升沟通效率
                  </li>
                  <li className="flex items-center text-gray-300">
                    <i class="fa-solid fa-check-circle text-green-400 mr-2"></i>
                    智能情绪识别，优化沟通策略
                  </li>
                  <li className="flex items-center text-gray-300">
                    <i class="fa-solid fa-check-circle text-green-400 mr-2"></i>
                    多语言支持，覆盖更广泛人群
                  </li>
                  <li className="flex items-center text-gray-300">
                    <i class="fa-solid fa-check-circle text-green-400 mr-2"></i>
                    实时数据分析，优化运营效果
                  </li>
                </ul>
                  <Link 
                    to="/compare/ai-call-center" 
                    className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-lg hover:opacity-90 transition-opacity shadow-lg w-fit"
                    onClick={(e) => {
                      e.preventDefault();
                      window.location.href = '/compare/ai-call-center';
                    }}
                  >
                    了解更多
                  </Link>
              </div>
              <div className="relative h-96 lg:h-auto">
                <div className="absolute inset-0 bg-[url('https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=AI%20call%20center%20with%20futuristic%20interface%20and%20holograms&sign=bee839655e5b4b3720ff66c325e16071')] bg-cover bg-center opacity-80"></div>
                <div className="absolute inset-0 bg-gradient-to-r from-blue-900/50 to-purple-900/50"></div>
                <div className="absolute bottom-0 left-0 right-0 p-6 bg-gradient-to-t from-black/80 to-transparent">
                  <h3 className="text-xl font-semibold text-white font-['Rajdhani']">技术参数</h3>
                  <div className="grid grid-cols-2 gap-4 mt-3">
                    <div className="bg-white/10 backdrop-blur-sm p-3 rounded-lg border border-white/10">
                      <p className="text-sm text-blue-300 font-['Inter']">识别准确率</p>
                      <p className="text-2xl font-bold text-white font-['Rajdhani']">98%</p>
                    </div>
                    <div className="bg-white/10 backdrop-blur-sm p-3 rounded-lg border border-white/10">
                      <p className="text-sm text-purple-300 font-['Inter']">响应速度</p>
                      <p className="text-2xl font-bold text-white font-['Rajdhani']">0.5s</p>
                    </div>
                    <div className="bg-white/10 backdrop-blur-sm p-3 rounded-lg border border-white/10">
                      <p className="text-sm text-green-300 font-['Inter']">并发量</p>
                      <p className="text-2xl font-bold text-white font-['Rajdhani']">1000+</p>
                    </div>
                    <div className="bg-white/10 backdrop-blur-sm p-3 rounded-lg border border-white/10">
                      <p className="text-sm text-yellow-300 font-['Inter']">识别速度</p>
                      <p className="text-2xl font-bold text-white font-['Rajdhani']">200ms</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
