import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Background from '@/components/Background';
import Footer from '@/components/Footer';
import { newsData } from '@/data/mock';
import { motion, AnimatePresence } from 'framer-motion';

export default function News() {
  const [newsList, setNewsList] = useState(newsData);
  const [expandedNews, setExpandedNews] = useState<number | null>(null);

  useEffect(() => {
    const savedNews = localStorage.getItem('newsData');
    if (savedNews) {
      setNewsList(JSON.parse(savedNews));
    }
  }, []);

  const toggleExpand = (id: number) => {
    setExpandedNews(expandedNews === id ? null : id);
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-blue-900/10 to-purple-900/10">
      <main className="flex-1 relative">
        <Background />
        <div className="container mx-auto px-4 py-12 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="text-center mb-12"
          >
            <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
              公司动态
            </h1>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              了解惠他智能的最新发展和行业动态
            </p>
          </motion.div>

          <div className="max-w-4xl mx-auto space-y-8">
            {newsList.map(news => (
              <motion.div 
                key={news.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="bg-gradient-to-br from-gray-800/50 to-blue-900/30 backdrop-blur-sm p-8 rounded-2xl shadow-xl border border-white/10 hover:border-blue-400/30 transition-all"
              >
                <div 
                  className="cursor-pointer"
                  onClick={() => toggleExpand(news.id)}
                >
                  <div className="flex justify-between items-start mb-4">
                    <h2 className="text-2xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400">
                      {news.title}
                    </h2>
                    <span className="text-blue-300">{news.date}</span>
                  </div>
                  
                  <AnimatePresence>
                    {expandedNews === news.id ? (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="overflow-hidden"
                      >
                        <div className="prose max-w-none text-gray-300 space-y-4">
                          {news.content.split('\n').map((paragraph, i) => (
                         <p key={i}>{paragraph}</p>
                       ))}
                     </div>
                     <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                       {news.media.map((media, index) => (
                         <div key={index} className="rounded-lg overflow-hidden">
                           {media.type === 'image' ? (
                             <img 
                               src={media.url} 
                               alt={`媒体 ${index + 1}`}
                               className="w-full h-48 object-cover"
                             />
                           ) : (
                             <video 
                               src={media.url} 
                               controls
                               className="w-full h-48 object-cover"
                             />
                           )}
                         </div>
                       ))}
                     </div>
                       </motion.div>
                    ) : (
                      <motion.p 
                        className="text-gray-400"
                        initial={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                      >
                        {news.summary}
                      </motion.p>
                    )}
                  </AnimatePresence>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}