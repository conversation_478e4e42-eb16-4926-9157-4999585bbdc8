import { useState } from 'react';
import Background from '@/components/Background';
import Footer from '@/components/Footer';
import { partnerCategories, partnersData } from '@/data/mock';

export default function Partners() {
  const [activeTab, setActiveTab] = useState('partners');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const filteredPartners = selectedCategory === 'all' 
    ? partnersData 
    : partnersData.filter(partner => partner.category === selectedCategory);

  return (
    <div className="min-h-screen flex flex-col">
      
      <main className="flex-1 relative">
        <Background />
        <div className="container mx-auto px-4 py-12 relative z-10">
          <h1 className="text-3xl font-bold text-center mb-8 text-blue-600">合作伙伴</h1>
          
          {/* 分类筛选 */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            <button
              onClick={() => setSelectedCategory('all')}
              className={`px-6 py-2 rounded-full font-medium transition-colors ${
                selectedCategory === 'all' 
                  ? 'bg-green-600 text-white' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              全部
            </button>
            {partnerCategories.map(category => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.value)}
                className={`px-6 py-2 rounded-full font-medium transition-colors ${
                  selectedCategory === category.value 
                    ? 'bg-green-600 text-white' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>

          {/* 合作伙伴展示墙 */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 px-4">
            {filteredPartners.map(partner => (
              <div 
                key={partner.id} 
                className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow min-w-[150px]"
              >
                <div className="p-4 sm:p-6 flex flex-col items-center">
                  <img 
                    src={partner.logo} 
                    alt={partner.name}
                    className="w-16 h-16 sm:w-24 sm:h-24 object-contain mb-3 sm:mb-4"
                  />
                  <h3 className="text-base sm:text-lg font-semibold text-center mb-1 sm:mb-2">{partner.name}</h3>
                  <p className="text-gray-600 text-xs sm:text-sm text-center">{partner.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}