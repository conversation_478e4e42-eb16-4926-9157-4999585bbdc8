// 案例中心页面组件
import { useState, useRef, useEffect, useContext } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import Background from '@/components/Background';
import Footer from '@/components/Footer';
import { getCases } from '@/api/cases';
import { CaseStudy } from '@/api/cases';
import { AuthContext } from '@/contexts/AuthContext';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'sonner';
import CaseDetail from '@/pages/CaseDetail';

/**
 * 案例中心页面组件
 * 展示公司案例列表，支持点击查看详情
 */
export default function Cases() {
  // 案例数据状态
  const [cases, setCases] = useState<CaseStudy[]>([]);
  const [isPlaying, setIsPlaying] = useState<Record<number, boolean>>({});
  const [isMuted, setIsMuted] = useState<Record<number, boolean>>({});
  const [progress, setProgress] = useState<Record<number, number>>({});
  const audioRefs = useRef<Record<number, HTMLAudioElement | null>>({});
  // 加载状态
  const [loading, setLoading] = useState(true);
  // 认证状态
  const { isAuthenticated } = useContext(AuthContext);
  const navigate = useNavigate();
  // 当前选中的案例
  const [selectedCase, setSelectedCase] = useState<CaseStudy | null>(null);

  // 组件挂载时获取案例数据
  useEffect(() => {
    const fetchCases = async () => {
      try {
        setLoading(true);
        const data = await getCases();
        setCases(data);
      } catch (error) {
        console.error('Failed to fetch cases:', error);
        toast.error('加载案例失败');
      } finally {
        setLoading(false);
      }
    };
    fetchCases();
  }, []);

  const togglePlayPause = (id: number) => {
    try {
      if (audioRefs.current[id]) {
        if (isPlaying[id]) {
          audioRefs.current[id]?.pause();
        } else {
          if (typeof audioRefs.current[id]?.play === 'function') {
            const playPromise = audioRefs.current[id]?.play();
            if (playPromise !== undefined) {
              playPromise.catch(error => {
                console.error('播放失败:', error);
                setIsPlaying(prev => ({...prev, [id]: false}));
              });
            }
          }
        }
        setIsPlaying(prev => ({...prev, [id]: !prev[id]}));
      }
    } catch (error) {
      console.error('音频操作失败:', error);
      setIsPlaying(prev => ({...prev, [id]: false}));
    }
  };

  const toggleMute = (id: number) => {
    try {
      if (audioRefs.current[id]) {
        audioRefs.current[id]!.muted = !isMuted[id];
        setIsMuted(prev => ({...prev, [id]: !prev[id]}));
      }
    } catch (error) {
      console.error('静音操作失败:', error);
    }
  };

  const handleTimeUpdate = (id: number) => {
    if (audioRefs.current[id]) {
      const audio = audioRefs.current[id]!;
      const duration = audio.duration || 1;
      const currentTime = audio.currentTime;
      setProgress(prev => ({...prev, [id]: (currentTime / duration) * 100}));
    }
  };

  const handleCaseClick = (caseItem: CaseStudy) => {
    setSelectedCase(caseItem);
  };

  const closeModal = () => {
    setSelectedCase(null);
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-blue-900/10 to-purple-900/10">
      <main className="flex-1 relative">
        <Background />
        <div className="container mx-auto px-4 py-12 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="text-center mb-12"
          >
            <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
              案例中心
            </h1>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              创新金融科技引领者，打造不良资产运营新生态
            </p>
          </motion.div>

          {loading ? (
            <div className="flex flex-col items-center justify-center py-12">
              <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mb-4"></div>
              <p className="text-gray-300">加载中...</p>
            </div>
          ) : cases.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {cases.map((caseItem, index) => {
                if (caseItem.audio) {
                  audioRefs.current[caseItem.id] = null;
                }
                
                return (
                  <div key={caseItem.id} className="h-full">
                    {caseItem.audio && (
                      <audio
                        ref={el => audioRefs.current[caseItem.id] = el}
                        src={caseItem.audio.url}
                        onTimeUpdate={() => handleTimeUpdate(caseItem.id)}
                        onEnded={() => setIsPlaying(prev => ({...prev, [caseItem.id]: false}))}
                        className="hidden"
                      />
                    )}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ 
                        opacity: 1, 
                        y: 0,
                        transition: {
                          delay: index * 0.1,
                          duration: 0.4,
                          ease: [0.16, 1, 0.3, 1]
                        }
                      }}
                      whileHover={{ 
                        y: -8,
                        transition: { type: "spring", stiffness: 400, damping: 10 }
                      }}
                      className="h-full"
                    >
                      <div 
                        onClick={() => handleCaseClick(caseItem)}
                        className="bg-gradient-to-br from-gray-800/50 to-blue-900/30 backdrop-blur-sm p-6 rounded-2xl shadow-xl border border-white/10 hover:border-blue-400/30 transition-all h-full cursor-pointer card-hover-3d flex flex-col"
                        style={{ minHeight: '400px' }}
                      >
                        <h2 className="text-xl font-semibold mb-3 text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-300">
                          {caseItem.title}
                        </h2>
                        <p className="text-gray-300 mb-4 flex-grow">{caseItem.description}</p>
                        
                        {caseItem.audio && (
                          <div className="mt-4 bg-gradient-to-r from-blue-900/30 to-purple-900/30 backdrop-blur-sm p-3 rounded-lg border border-white/10">
                            <div className="flex items-center space-x-3">
                              <button 
                                onClick={(e) => {
                                  e.stopPropagation();
                                  togglePlayPause(caseItem.id);
                                }}
                                className="bg-blue-600 text-white p-2 rounded-full hover:bg-blue-700 transition-colors"
                              >
                                <i class={`fa-solid ${isPlaying[caseItem.id] ? 'fa-pause' : 'fa-play'}`}></i>
                              </button>
                              <div className="flex-1">
                                <div className="h-1 bg-gray-700 rounded-full">
                                  <div 
                                    className="h-full bg-gradient-to-r from-blue-400 to-purple-500 rounded-full" 
                                    style={{ width: `${progress[caseItem.id] || 0}%` }}
                                  ></div>
                                </div>
                              </div>
                              <button 
                                onClick={(e) => {
                                  e.stopPropagation();
                                  toggleMute(caseItem.id);
                                }}
                                className="text-gray-300 hover:text-white"
                              >
                                <i class={`fa-solid ${isMuted[caseItem.id] ? 'fa-volume-xmark' : 'fa-volume-high'}`}></i>
                              </button>
                            </div>
                            <div className="text-xs text-gray-400 mt-1">
                              {caseItem.audio.title || '案例音频'} {caseItem.audio.duration && `(${caseItem.audio.duration})`}
                            </div>
                          </div>
                        )}
                        
                        <div className="flex flex-wrap gap-2 mt-4">
                          {caseItem.tags.map(tag => (
                            <span 
                              key={tag} 
                              className="bg-gradient-to-r from-blue-500/30 to-purple-500/30 text-blue-200 px-3 py-1 rounded-full text-sm border border-blue-400/20"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>
                    </motion.div>
                  </div>
                );
              })}
            </div>
          ) : (
            <motion.div 
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-12"
            >
              <div className="bg-gradient-to-br from-gray-800/50 to-blue-900/30 backdrop-blur-sm p-8 rounded-2xl shadow-xl border border-white/10 max-w-md mx-auto">
                <i class="fa-solid fa-folder-open text-4xl text-blue-400 mb-4"></i>
                <h3 className="text-xl font-semibold text-white mb-2">暂无案例数据</h3>
                <p className="text-gray-400">我们正在准备更多精彩案例</p>
              </div>
            </motion.div>
          )}

          {/* 案例详情模态框 */}
          <AnimatePresence>
            {selectedCase && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4"
                onClick={closeModal}
              >
                <motion.div
                  initial={{ scale: 0.9, y: 50 }}
                  animate={{ scale: 1, y: 0 }}
                  exit={{ scale: 0.9, y: 50 }}
                  className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto"
                  onClick={(e) => e.stopPropagation()}
                >
                  <CaseDetail caseItem={selectedCase} onClose={closeModal} />
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </main>
      <Footer />
    </div>
  );
}