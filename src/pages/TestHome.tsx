import { memo } from 'react';
import Footer from '@/components/Footer';

const TestHome = memo(() => {
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-gray-900 to-gray-950">
      <main className="flex-1 relative">
        <div className="container mx-auto px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
              惠他智能
            </h1>
            <h2 className="text-2xl md:text-4xl font-semibold bg-gradient-to-r from-blue-300 to-purple-400 bg-clip-text text-transparent mb-8">
              创新金融科技引领者
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              打造不良资产运营新生态，通过科技手段重塑行业生态
            </p>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
});

TestHome.displayName = 'TestHome';

export default TestHome;
