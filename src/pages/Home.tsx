import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import Footer from '@/components/Footer';
import VideoCarousel from '@/components/VideoCarousel';
import DataDashboard from '@/components/DataDashboard';
import OptimizedImage from '@/components/OptimizedImage';
import useLazyLoad from '@/hooks/useLazyLoad';
import { animationConfig, conditionalAnimation } from '@/lib/animations';
import { videoData, dashboardData, partnersData, newsData } from '@/data/mock';



export default function Home() {
  const [settings, setSettings] = useState({
    showPartners: true,
    showNews: true
  });

  const [partnersRef, partnersVisible] = useLazyLoad();
  const [newsRef, newsVisible] = useLazyLoad();

  useEffect(() => {
    const savedSettings = localStorage.getItem('systemSettings');
    if (savedSettings) {
      setSettings(JSON.parse(savedSettings));
    }
  }, []);

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-gray-900 to-gray-950">
      <main className="flex-1 relative">
        {/* 科技感背景 */}
        <div className="absolute inset-0 -z-10 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-900/30 via-purple-900/20 to-transparent animate-gradient-flow"></div>
          <div className="absolute inset-0 bg-[url('https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=tech%20background%20with%20particles%20and%20circuits&sign=f1a84699569b7cacb01857badaee5564')] opacity-20 bg-cover bg-center"></div>
          <div className="absolute inset-0 bg-[url('https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=tech%20grid%20lines%20background&sign=bd36f3f8d5134a4084c0b071a25ad087')] opacity-10 bg-cover bg-center"></div>
        </div>

          {/* 首页大图 - 添加动态效果 */}
          <div className="relative w-full h-[50vh] sm:h-[60vh] md:h-[70vh] max-h-[800px] min-h-[300px] overflow-hidden">
            <OptimizedImage
              src="https://huitaweb-prd.oss-cn-shenzhen.aliyuncs.com/website/%E9%A6%96%E9%A1%B5.jpg"
              alt="惠他智能首页"
              className="brightness-90"
              style={{ objectPosition: 'center center' }}
              priority={true}
              sizes="100vw"
            />
            <div className="absolute inset-0 bg-black/24 flex flex-col items-center justify-center pt-8">
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              {/* 科幻风格大标题 */}
              <div className="relative z-10 text-center px-6 lg:px-8 py-4">
               <motion.h1 
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ 
                    opacity: 1, 
                    y: 0,
                    transition: {
                      duration: 0.8,
                      ease: [0.16, 1, 0.3, 1]
                    }
                  }}
                  className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent neon-effect"
                >
                  创新金融科技引领者
                </motion.h1>
                <motion.h2 
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ 
                    opacity: 1, 
                    y: 0,
                    transition: {
                      delay: 0.2,
                      duration: 0.6,
                      ease: [0.16, 1, 0.3, 1]
                    }
                  }}
                  className="text-2xl md:text-4xl font-semibold bg-gradient-to-r from-blue-300 to-purple-400 bg-clip-text text-transparent"
                >
                  打造不良资产运营新生态
                </motion.h2>
              </div>
            </div>
          </div>
        
        {/* 保留原有容器结构 */}
        <div className="container mx-auto px-6 lg:px-8 py-16"></div>

        <div className="container mx-auto px-6 lg:px-8 space-y-24 pb-24">
          {/* 个贷"黑石"模式介绍 - 新设计 */}
          <motion.section
            {...conditionalAnimation(animationConfig.inView)}
            className="bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-sm rounded-lg shadow-2xl overflow-hidden border border-gray-700/50 p-8 mb-12"
          >
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-gray-200 to-gray-400 mb-2">
                个贷"黑石"模式
              </h2>
              <p className="text-gray-400 text-lg">专业资产管理解决方案</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* 资产收购 */}
              <motion.div
                {...conditionalAnimation(animationConfig.hoverLift)}
                className="bg-gray-800/90 backdrop-blur-sm p-6 rounded-lg border border-gray-600/50 hover:border-blue-500/50 transition-all"
              >
                <div className="flex flex-col items-center text-center h-full">
                  <div className="bg-gray-700 text-blue-400 rounded-lg w-12 h-12 flex items-center justify-center mb-4 shadow-inner">
                    <i class="fa-solid fa-hand-holding-dollar text-xl"></i>
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-3">资产收购</h3>
                  <p className="text-gray-400">挖掘低价潜力资产，精准识别投资机会</p>
                  <div className="mt-4 w-16 h-1 bg-gradient-to-r from-blue-500 to-gray-500 rounded-full"></div>
                </div>
              </motion.div>
              
              {/* 资产运营 - 重点突出 */}
              <motion.div 
                whileHover={{ 
                  y: -8,
                  rotateX: 5,
                  boxShadow: "0 20px 25px -5px rgba(59, 130, 246, 0.3)"
                }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
                className="bg-gray-800/90 backdrop-blur-sm p-6 rounded-lg border border-blue-500/50 shadow-lg shadow-blue-500/20 relative z-10 transform-style-preserve-3d"
              >
                <div className="absolute -top-3 -right-3 bg-blue-600 text-white text-xs font-bold px-3 py-1 rounded-full shadow-md">
                  核心
                </div>
                <div className="flex flex-col items-center text-center h-full">
                  <div className="bg-gray-700 text-blue-400 rounded-lg w-12 h-12 flex items-center justify-center mb-4 shadow-inner">
                    <i class="fa-solid fa-chart-line text-xl"></i>
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-3">资产运营</h3>
                  <p className="text-gray-400">通过债务和解方案，实现稳定现金流回报</p>
                  <div className="mt-4 w-16 h-1 bg-gradient-to-r from-blue-500 to-gray-500 rounded-full"></div>
                </div>
              </motion.div>
              
              {/* 退出机制 */}
              <motion.div 
                whileHover={{ 
                  y: -8,
                  rotateX: 5,
                  boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.3)"
                }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
                className="bg-gray-800/90 backdrop-blur-sm p-6 rounded-lg border border-gray-600/50 hover:border-blue-500/50 transition-all transform-style-preserve-3d"
              >
                <div className="flex flex-col items-center text-center h-full">
                  <div className="bg-gray-700 text-blue-400 rounded-lg w-12 h-12 flex items-center justify-center mb-4 shadow-inner">
                    <i class="fa-solid fa-arrow-right-from-bracket text-xl"></i>
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-3">退出机制</h3>
                  <p className="text-gray-400">巧用ABS等金融工具实现资本化退出</p>
                  <div className="mt-4 w-16 h-1 bg-gradient-to-r from-blue-500 to-gray-500 rounded-full"></div>
                </div>
              </motion.div>
            </div>
          </motion.section>

          {/* 数据仪表盘 - 已根据用户要求隐藏金额和成功率数据 */}
          {false && (
            <motion.section 
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="bg-gray-800/50 backdrop-blur-sm rounded-2xl shadow-2xl overflow-hidden border border-white/10"
            >
              <DataDashboard data={dashboardData} />
            </motion.section>
          )}
          
          {/* 债务人运营模式介绍 - 新设计 */}
          <motion.section
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="bg-gradient-to-br from-teal-900/20 to-blue-900/20 backdrop-blur-sm rounded-3xl shadow-2xl overflow-hidden border border-teal-400/20 p-8 mb-12"
          >
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-teal-300 to-blue-400 mb-2">
                以债务人运营模式重新定义个贷不良资产行业
              </h2>
              <p className="text-teal-200 text-lg">通过创新模式发掘债务人长期价值</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* 发掘债务人的终生价值 */}
              <motion.div 
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.98 }}
                className="bg-gradient-to-br from-teal-800/30 to-blue-800/30 backdrop-blur-sm p-6 rounded-2xl border border-teal-400/30 hover:border-blue-400/50 transition-all"
              >
                <div className="flex flex-col items-center text-center h-full">
                  <div className="bg-teal-500/20 text-teal-300 rounded-xl w-14 h-14 flex items-center justify-center mb-4 shadow-lg">
                    <i class="fa-solid fa-gem text-2xl"></i>
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-3">发掘债务人的终生价值</h3>
                  <p className="text-teal-100 flex-grow">欠款还清后的债务人更具服务价值</p>
                  <div className="mt-4 w-full h-1 bg-gradient-to-r from-teal-500/50 to-blue-500/50 rounded-full"></div>
                </div>
              </motion.div>
              
              {/* 债务人运营平台 - 重点突出 */}
              <motion.div 
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.98 }}
                className="bg-gradient-to-br from-blue-800/30 to-indigo-800/30 backdrop-blur-sm p-6 rounded-2xl border border-blue-400/30 hover:border-indigo-400/50 transition-all relative z-10 shadow-lg shadow-blue-500/20"
              >
                <div className="absolute -top-3 -right-3 bg-blue-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-md">
                  核心
                </div>
                <div className="flex flex-col items-center text-center h-full">
                  <div className="bg-blue-500/20 text-blue-300 rounded-xl w-14 h-14 flex items-center justify-center mb-4 shadow-lg">
                    <i class="fa-solid fa-user-tie text-2xl"></i>
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-3">债务人运营平台</h3>
                  <p className="text-blue-100 flex-grow">帮助债务人做财务规划、生活规划</p>
                  <div className="mt-4 w-full h-1 bg-gradient-to-r from-blue-500/50 to-indigo-500/50 rounded-full"></div>
                </div>
              </motion.div>
              
              {/* 创造资产的长尾效应 */}
              <motion.div 
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.98 }}
                className="bg-gradient-to-br from-indigo-800/30 to-purple-800/30 backdrop-blur-sm p-6 rounded-2xl border border-indigo-400/30 hover:border-purple-400/50 transition-all"
              >
                <div className="flex flex-col items-center text-center h-full">
                  <div className="bg-indigo-500/20 text-indigo-300 rounded-xl w-14 h-14 flex items-center justify-center mb-4 shadow-lg">
                    <i class="fa-solid fa-chart-pie text-2xl"></i>
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-3">创造资产的长尾效应</h3>
                  <p className="text-indigo-100 flex-grow">债务人的终生价值远超债务本身</p>
                  <div className="mt-4 w-full h-1 bg-gradient-to-r from-indigo-500/50 to-purple-500/50 rounded-full"></div>
                </div>
              </motion.div>
            </div>
          </motion.section>

          {/* 合作伙伴展示墙 */}
          {settings.showPartners && (
            <motion.section
              ref={partnersRef}
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="bg-gray-800/50 backdrop-blur-sm rounded-2xl shadow-2xl overflow-hidden border border-white/10 p-6 md:p-8"
            >
              <h2 className="text-3xl font-bold text-center mb-8 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400">
                战略合作伙伴
              </h2>
                {partnersVisible && (
                   <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-4 px-2 md:px-4">
                     {partnersData.map(partner => (
                       <motion.div 
                         key={partner.id} 
                         whileHover={{ y: -8 }}
                         className="group min-w-[120px]"
                       >
                         <div className="bg-gray-900/80 backdrop-blur-sm p-3 sm:p-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 h-full flex flex-col items-center border border-gray-700/50 hover:border-blue-400/30">
                           <OptimizedImage
                             src={partner.logo}
                             alt={partner.name}
                             className="w-14 h-14 sm:w-16 sm:h-16 md:w-20 md:h-20 object-contain mb-2 sm:mb-3 group-hover:scale-110 transition-transform"
                           />
                           <h3 className="text-sm sm:text-base font-semibold text-center text-white mb-1">{partner.name}</h3>
                           <p className="text-gray-400 text-xs text-center px-1 line-clamp-2">{partner.description}</p>
                         </div>
                       </motion.div>
                     ))}
                   </div>
                )}
              </motion.section>
            )}

          {/* 公司动态卡片列表 - 已根据系统设置隐藏 */}
          {false && settings.showNews && (
            <motion.section
              ref={newsRef}
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="bg-gray-800/50 backdrop-blur-sm rounded-2xl shadow-2xl overflow-hidden border border-white/10 p-10"
            >
              <h2 className="text-3xl font-bold text-center mb-12 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400">
                最新动态
              </h2>
              {newsVisible && (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                   {newsData.map(news => (
                      <motion.div 
                        key={news.id} 
                        whileHover={{ y: -8 }}
                        className="bg-gray-900/80 backdrop-blur-sm rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 border border-gray-700/50 hover:border-blue-400/30"
                      >
                        <div className="relative w-full h-56 bg-gray-800">
                          <img 
                            src={news.media[0].url} 
                            alt={news.title}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              e.currentTarget.src = 'https://placehold.co/600x400?text=Image+Not+Available';
                            }}
                          />
                          {news.media[0].type === 'video' && (
                            <div className="absolute inset-0 flex items-center justify-center bg-black/30">
                              <div className="bg-blue-600 text-white p-4 rounded-full">
                                <i className="fa-solid fa-play"></i>
                              </div>
                            </div>
                          )}
                        </div>
                        <div className="p-6">
                          <div className="flex justify-between items-start mb-4">
                            <h3 className="text-xl font-semibold text-white">{news.title}</h3>
                            <span className="text-sm text-gray-400">{news.date}</span>
                          </div>
                          <p className="text-gray-400 mb-6">{news.summary}</p>
                          <Link 
                            to="/news" 
                            className="text-blue-400 hover:text-blue-300 font-medium flex items-center gap-2"
                          >
                             了解更多 <i className="fa-solid fa-arrow-right"></i>
                          </Link>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                  <div className="mt-10 text-center">
                    <Link 
                      to="/news"
                      className="inline-block bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-lg hover:opacity-90 transition-opacity shadow-lg"
                    >
                      查看更多动态
                    </Link>
                  </div>
                </>
              )}
            </motion.section>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
}
