import { motion } from 'framer-motion';
import Background from '@/components/Background';
import Footer from '@/components/Footer';
import OptimizedImage from '@/components/OptimizedImage';

interface ContactData {
  email: string;
  phone: string;
  address: string;
  qrCode: string;
  images: string[];
}

export default function About() {
  const contactData = {
    email: '<EMAIL>',
    phone: '************',
    address: '佛山顺德区美的置业写字楼1903、1904号',
    qrCode: 'https://space.coze.cn/api/coze_space/gen_image?image_size=square&prompt=company%20contact%20qr%20code&sign=9829ad9d303e0b897dc4e3693dc23e9a',
    images: [
      'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=company%20office%20building&sign=5d770bba3768c3273546d6c40488753a',
      'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=company%20team%20photo&sign=e691cc19f8eb812753d149314477ef3c'
    ]
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-blue-900/10 to-purple-900/10">
      <main className="flex-1 relative">
        <Background />
        <div className="container mx-auto px-4 py-12 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="text-center mb-12"
          >
            <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
              关于惠他智能
            </h1>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              创新金融科技引领者，打造不良资产运营新生态
            </p>
          </motion.div>

          <div className="max-w-4xl mx-auto space-y-8">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="bg-gradient-to-br from-gray-800/50 to-blue-900/30 backdrop-blur-sm p-8 rounded-2xl shadow-xl border border-white/10 hover:border-blue-400/30 transition-all"
          >
            <h2 className="text-2xl font-semibold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400">
              惠他智能公司简介
            </h2>
            <div className="space-y-4 text-gray-300">
              <p>
                我们是一家专注于个人信贷不良资产投资和运营的金融科技公司，以AI驱动和债务人运营为核心，致力于通过科技手段重塑行业生态。
              </p>
              <p>
                作为行业内的创新标杆，我们首创"普惠化债"理念，将传统催收升级为债务人财务管理服务，帮助债务人走出债务困境。
              </p>
              <p>
                公司创始人团队均来自于国内外知名高校，在金融、科技、法律方面具备丰富的专业知识和庞大的业务资源。
              </p>
            </div>
          </motion.div>

          {/* 企业价值观部分 */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className="bg-gradient-to-br from-gray-800/50 to-purple-900/30 backdrop-blur-sm p-8 rounded-2xl shadow-xl border border-white/10 hover:border-purple-400/30 transition-all"
          >
            <h2 className="text-2xl font-semibold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-blue-400">
              企业价值观
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <motion.div 
                whileHover={{ scale: 1.02 }}
                className="bg-gradient-to-br from-blue-900/30 to-purple-900/30 backdrop-blur-sm p-6 rounded-xl border border-white/10"
              >
                <div className="flex items-start">
                  <div className="bg-blue-500/20 text-blue-400 rounded-full w-10 h-10 flex items-center justify-center mr-4">
                    <i className="fa-solid fa-heart"></i>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">科技向善普惠利他</h3>
                    <p className="text-gray-400">我们坚信科技的力量应当用于造福社会，通过技术创新实现普惠金融，帮助更多人走出困境。</p>
                  </div>
                </div>
              </motion.div>
              <motion.div
                whileHover={{ scale: 1.02 }}
                className="bg-gradient-to-br from-purple-900/30 to-blue-900/30 backdrop-blur-sm p-6 rounded-xl border border-white/10"
              >
                <div className="flex items-start">
                  <div className="bg-purple-500/20 text-purple-400 rounded-full w-10 h-10 flex items-center justify-center mr-4">
                    <i className="fa-solid fa-hand-holding-heart"></i>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">帮助每一位债务人走出债务困境</h3>
                    <p className="text-gray-400">我们致力于为每一位债务人提供个性化解决方案，帮助他们重建财务健康，重返正常生活。</p>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>

            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
              className="bg-gradient-to-br from-gray-800/50 to-purple-900/30 backdrop-blur-sm p-8 rounded-2xl shadow-xl border border-white/10 hover:border-purple-400/30 transition-all"
            >
              <h2 className="text-2xl font-semibold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-blue-400">
                我们的优势
              </h2>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <div className="bg-blue-500/20 text-blue-400 rounded-full w-8 h-8 flex items-center justify-center mr-4">
                    <i className="fa-solid fa-microchip"></i>
                  </div>
                  <div>
                    <h3 className="font-medium text-white">AI驱动的智能债务运营系统</h3>
                    <p className="text-gray-400 text-sm">采用前沿AI技术提升运营效率</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <div className="bg-purple-500/20 text-purple-400 rounded-full w-8 h-8 flex items-center justify-center mr-4">
                    <i className="fa-solid fa-lightbulb"></i>
                  </div>
                  <div>
                    <h3 className="font-medium text-white">创新的普惠化债模式</h3>
                    <p className="text-gray-400 text-sm">重新定义债务解决方案</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <div className="bg-green-500/20 text-green-400 rounded-full w-8 h-8 flex items-center justify-center mr-4">
                    <i className="fa-solid fa-users"></i>
                  </div>
                  <div>
                    <h3 className="font-medium text-white">专业的金融科技团队</h3>
                    <p className="text-gray-400 text-sm">行业资深专家组成</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <div className="bg-yellow-500/20 text-yellow-400 rounded-full w-8 h-8 flex items-center justify-center mr-4">
                    <i className="fa-solid fa-network-wired"></i>
                  </div>
                  <div>
                    <h3 className="font-medium text-white">丰富的行业资源</h3>
                    <p className="text-gray-400 text-sm">与多家金融机构深度合作</p>
                  </div>
                </li>
              </ul>
            </motion.div>

            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
              className="bg-gradient-to-br from-gray-800/50 to-green-900/30 backdrop-blur-sm p-8 rounded-2xl shadow-xl border border-white/10 hover:border-green-400/30 transition-all"
            >
              <h2 className="text-2xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-blue-400 mb-6">
                联系我们
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-6">
                  <div className="space-y-4 text-gray-300">
                    <p className="flex items-center gap-3">
                      <div className="bg-blue-500/20 text-blue-400 rounded-full w-8 h-8 flex items-center justify-center">
                        <i className="fa-solid fa-envelope"></i>
                      </div>
                      <span>邮箱：{contactData.email}</span>
                    </p>
                    <p className="flex items-center gap-3">
                      <div className="bg-purple-500/20 text-purple-400 rounded-full w-8 h-8 flex items-center justify-center">
                        <i className="fa-solid fa-phone"></i>
                      </div>
                      <span>电话：{contactData.phone}</span>
                    </p>
                    <p className="flex items-center gap-3">
                      <div className="bg-green-500/20 text-green-400 rounded-full w-8 h-8 flex items-center justify-center">
                        <i className="fa-solid fa-location-dot"></i>
                      </div>
                      <span>地址：{contactData.address}</span>
                    </p>
                  </div>
                  
                  <div className="mt-8">
                    <h3 className="text-lg font-semibold text-blue-300 mb-3">扫码关注我们</h3>
                    <OptimizedImage
                      src={contactData.qrCode}
                      alt="微信公众号二维码"
                      className="w-32 h-32 object-contain border border-gray-600/50 rounded-lg"
                    />
                  </div>
                </div>

                <div>
                  <h2 className="text-2xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-blue-400 mb-6">
                    公司实景
                  </h2>
                  <div className="grid grid-cols-1 gap-4">
                    {contactData.images.map((img, index) => (
                      <motion.div 
                        key={index}
                        whileHover={{ scale: 1.02 }}
                        className="overflow-hidden rounded-lg border border-gray-600/50"
                      >
                        <OptimizedImage
                          src={img}
                          alt={`公司实景 ${index + 1}`}
                          className="w-full h-40 object-cover"
                        />
                      </motion.div>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
