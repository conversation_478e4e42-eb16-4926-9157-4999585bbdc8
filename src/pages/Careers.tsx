import { useState } from 'react';
import Background from '@/components/Background';
import Footer from '@/components/Footer';
import { jobPositions } from '@/data/mock';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

export default function Careers() {
  const [expandedJob, setExpandedJob] = useState<number | null>(null);
  const [jobs] = useState(jobPositions);

  const toggleJobDetails = (id: number) => {
    setExpandedJob(expandedJob === id ? null : id);
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-blue-900/10 to-purple-900/10">
      <main className="flex-1 relative">
        <Background />
        <div className="container mx-auto px-4 py-12 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="text-center mb-12"
          >
            <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
              加入惠他智能
            </h1>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              我们正在寻找志同道合的伙伴，共同推动普惠化债创新模式的发展
            </p>
          </motion.div>

          <div className="max-w-4xl mx-auto space-y-6">
            {jobs.map((job) => (
              <motion.div 
                key={job.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <motion.div 
                  className="bg-white/90 backdrop-blur-sm rounded-xl shadow-lg overflow-hidden border border-blue-200/30 hover:border-blue-300/50 transition-all"
                  whileHover={{ y: -5 }}
                >
                  <div 
                    className="p-6 cursor-pointer flex justify-between items-center hover:bg-blue-50/30 transition-colors"
                    onClick={() => toggleJobDetails(job.id)}
                  >
                    <div>
                      <h2 className="text-xl font-semibold text-blue-600">{job.title}</h2>
                      <p className="text-gray-600">{job.department} · {job.salary}</p>
                    </div>
                    <div className="flex items-center gap-4">
                      {expandedJob === job.id ? (
                        <ChevronUp className="text-blue-600" />
                      ) : (
                        <ChevronDown className="text-blue-600" />
                      )}
                    </div>
                  </div>
                  
                  <AnimatePresence>
                    {expandedJob === job.id && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="overflow-hidden"
                      >
                        <div className="px-6 pb-6 border-t border-gray-100/50 pt-4">
                          <h3 className="font-medium text-lg mb-2 text-gray-800">职位描述</h3>
                          <p className="text-gray-700 mb-4">{job.description}</p>
                          
                          <h3 className="font-medium text-lg mb-2 text-gray-800">任职要求</h3>
                          <ul className="list-disc pl-5 space-y-1 mb-4 text-gray-700">
                            {job.requirements.map((req, index) => (
                              <li key={index}>{req}</li>
                            ))}
                          </ul>
                          
                          <h3 className="font-medium text-lg mb-2 text-gray-800">薪资福利</h3>
                          <ul className="list-disc pl-5 space-y-1 text-gray-700">
                            {job.benefits.map((benefit, index) => (
                              <li key={index}>{benefit}</li>
                            ))}
                          </ul>
                          {job.qrCode && (
                            <div className="mt-4">
                              <h3 className="font-medium text-lg mb-2 text-gray-800">岗位二维码</h3>
                              <img 
                                src={job.qrCode} 
                                alt="岗位二维码" 
                                className="w-32 h-32 object-contain border border-gray-300 rounded-lg"
                              />
                            </div>
                          )}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              </motion.div>
            ))}
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
