
import { useState, useEffect, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Background from '@/components/Background';
import Footer from '@/components/Footer';
import { useForm } from 'react-hook-form';
import { QrCode, Image as ImageIcon, Edit, Save, Upload } from 'lucide-react';
import { toast } from 'sonner';
import { AuthContext } from '@/App';

interface ContactData {
  email: string;
  phone: string;
  address: string;
  qrCode: string;
  images: string[];
}

export default function Contact() {
  const [isEditing, setIsEditing] = useState(false);
  const [contactData, setContactData] = useState<ContactData>({
    email: '<EMAIL>',
    phone: '************',
    address: '佛山顺德区美的置业写字楼1903、1904号',
    qrCode: 'https://space.coze.cn/api/coze_space/gen_image?image_size=square&prompt=company%20contact%20qr%20code&sign=9829ad9d303e0b897dc4e3693dc23e9a',
    images: [
      'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=company%20office%20building&sign=5d770bba3768c3273546d6c40488753a',
      'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=company%20team%20photo&sign=e691cc19f8eb812753d149314477ef3c'
    ]
  });
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const { register, handleSubmit, reset, setValue } = useForm<ContactData>();
  const { isAuthenticated } = useContext(AuthContext);

  useEffect(() => {
    const savedData = localStorage.getItem('contactData');
    if (savedData) {
      setContactData(JSON.parse(savedData));
    }
  }, []);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const onSubmit = (data: ContactData) => {
    const updatedData = {
      ...data,
      qrCode: contactData.qrCode,
      images: imagePreview 
        ? [...contactData.images.slice(0, -1), imagePreview] 
        : contactData.images
    };
    setContactData(updatedData);
    localStorage.setItem('contactData', JSON.stringify(updatedData));
    toast.success('联系方式已更新');
    setIsEditing(false);
    setImagePreview(null);
  };

  const toggleEdit = () => {
    if (isEditing) {
      reset();
    } else {
      setValue('email', contactData.email);
      setValue('phone', contactData.phone);
      setValue('address', contactData.address);
    }
    setIsEditing(!isEditing);
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-blue-900/10 to-purple-900/10">
      <main className="flex-1 relative">
        <Background />
        <div className="container mx-auto px-4 py-12 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="text-center mb-12"
          >
            <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
              联系我们
            </h1>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              我们期待与您建立联系，共同探讨合作机会
            </p>
          </motion.div>

          {isAuthenticated && (
            <div className="flex justify-end mb-6">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={toggleEdit}
                className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all"
              >
                {isEditing ? <Save size={20} /> : <Edit size={20} />}
                {isEditing ? '保存' : '编辑'}
              </motion.button>
            </div>
          )}

          {isEditing ? (
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="max-w-4xl mx-auto bg-gradient-to-br from-gray-800/50 to-blue-900/30 backdrop-blur-sm p-8 rounded-2xl shadow-xl border border-white/10"
            >
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                <div>
                  <label className="block text-gray-300 mb-2">邮箱</label>
                  <input
                    {...register('email', { required: '请输入邮箱' })}
                    className="w-full px-4 py-2 bg-gray-700/50 border border-gray-600/50 rounded-lg focus:ring-2 focus:ring-blue-500 text-white"
                  />
                </div>
                <div>
                  <label className="block text-gray-300 mb-2">电话</label>
                  <input
                    {...register('phone', { required: '请输入电话' })}
                    className="w-full px-4 py-2 bg-gray-700/50 border border-gray-600/50 rounded-lg focus:ring-2 focus:ring-blue-500 text-white"
                  />
                </div>
                <div>
                  <label className="block text-gray-300 mb-2">地址</label>
                  <input
                    {...register('address', { required: '请输入地址' })}
                    className="w-full px-4 py-2 bg-gray-700/50 border border-gray-600/50 rounded-lg focus:ring-2 focus:ring-blue-500 text-white"
                  />
                </div>
                <div>
                  <label className="block text-gray-300 mb-2">公司图片</label>
                  <div className="flex flex-wrap gap-4 mb-4">
                    {contactData.images.map((img, index) => (
                      <div key={index} className="relative group">
                        <img 
                          src={img} 
                          alt={`公司图片 ${index + 1}`}
                          className="w-32 h-32 object-cover rounded-lg border border-gray-600/50"
                        />
                        {isEditing && index === contactData.images.length - 1 && (
                          <div className="absolute inset-0 bg-black/50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                            <label className="cursor-pointer">
                              <Upload size={24} className="text-white" />
                              <input 
                                type="file" 
                                className="hidden" 
                                onChange={handleImageChange}
                                accept="image/*"
                              />
                            </label>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
                <div className="flex justify-end gap-4">
                  <button
                    type="button"
                    onClick={toggleEdit}
                    className="px-6 py-2 border border-gray-600/50 text-gray-300 rounded-lg hover:bg-gray-700/50 transition-colors"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    className="px-6 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:opacity-90 transition-opacity shadow-md"
                  >
                    保存更改
                  </button>
                </div>
              </form>
            </motion.div>
          ) : (
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="max-w-4xl mx-auto bg-gradient-to-br from-gray-800/50 to-blue-900/30 backdrop-blur-sm p-8 rounded-2xl shadow-xl border border-white/10"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-6">
                  <h2 className="text-2xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400">
                    联系方式
                  </h2>
                  <div className="space-y-4 text-gray-300">
                    <p className="flex items-center gap-3">
                      <div className="bg-blue-500/20 text-blue-400 rounded-full w-8 h-8 flex items-center justify-center">
                        <i class="fa-solid fa-envelope"></i>
                      </div>
                      <span>邮箱：{contactData.email}</span>
                    </p>
                    <p className="flex items-center gap-3">
                      <div className="bg-purple-500/20 text-purple-400 rounded-full w-8 h-8 flex items-center justify-center">
                        <i class="fa-solid fa-phone"></i>
                      </div>
                      <span>电话：{contactData.phone}</span>
                    </p>
                    <p className="flex items-center gap-3">
                      <div className="bg-green-500/20 text-green-400 rounded-full w-8 h-8 flex items-center justify-center">
                        <i class="fa-solid fa-location-dot"></i>
                      </div>
                      <span>地址：{contactData.address}</span>
                    </p>
                  </div>
                  
                  <div className="mt-8">
                    <h3 className="text-lg font-semibold text-blue-300 mb-3">扫码关注我们</h3>
                    <img 
                      src={contactData.qrCode} 
                      alt="微信公众号二维码"
                      className="w-32 h-32 object-contain border border-gray-600/50 rounded-lg"
                    />
                  </div>
                </div>

                <div>
                  <h2 className="text-2xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-blue-400 mb-6">
                    公司实景
                  </h2>
                  <div className="grid grid-cols-1 gap-4">
                    {contactData.images.map((img, index) => (
                      <motion.div 
                        key={index}
                        whileHover={{ scale: 1.02 }}
                        className="overflow-hidden rounded-lg border border-gray-600/50"
                      >
                        <img
                          src={img}
                          alt={`公司实景 ${index + 1}`}
                          className="w-full h-40 object-cover"
                        />
                      </motion.div>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
}
