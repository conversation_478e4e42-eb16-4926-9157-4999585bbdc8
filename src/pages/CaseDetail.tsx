import { useState, useRef } from 'react';
import { Play, Pause, Volume2, VolumeX, X } from 'lucide-react';
import { toast } from 'sonner';

interface CaseStudy {
  id: number;
  title: string;
  description: string;
  content: string;
  media: {
    type: 'image' | 'video';
    url: string;
    thumbnail?: string;
  }[];
  audio?: {
    url: string;
    title?: string;
    duration?: string;
  };
  tags: string[];
}

interface CaseDetailProps {
  caseItem: CaseStudy;
  onClose: () => void;
}

export default function CaseDetail({ caseItem, onClose }: CaseDetailProps) {
  const [playingVideo, setPlayingVideo] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [progress, setProgress] = useState(0);

  const togglePlayPause = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const toggleMute = () => {
    if (audioRef.current) {
      audioRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      const duration = audioRef.current.duration || 1;
      const currentTime = audioRef.current.currentTime;
      setProgress((currentTime / duration) * 100);
    }
  };

  return (
    <div className="relative">
      <button 
        onClick={onClose}
        className="absolute top-4 right-4 text-gray-400 hover:text-white z-50"
      >
        <X size={24} />
      </button>

      <div className="p-6">
        <h1 className="text-2xl font-bold text-center mb-6 text-blue-500">{caseItem.title}</h1>
        
        <div className="mb-6">
          <p className="text-gray-300 text-lg mb-6">{caseItem.description}</p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            {caseItem.media.map((media, index) => (
              <div key={index} className="relative group">
                {media.type === 'image' ? (
                  <img 
                    src={media.url} 
                    alt={`案例图片 ${index + 1}`}
                    className="w-full h-48 object-cover rounded-lg shadow-md"
                  />
                ) : (
                  <div className="relative">
                    <img 
                      src={media.thumbnail} 
                      alt="视频封面"
                      className="w-full h-48 object-cover rounded-lg shadow-md"
                    />
                    <button 
                      onClick={() => setPlayingVideo(media.url)}
                      className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 group-hover:bg-opacity-50 transition-all"
                    >
                      <div className="bg-blue-600 text-white p-4 rounded-full">
                        <Play size={24} />
                      </div>
                    </button>
                  </div>
                )}
              </div>
            ))}
          </div>

          {caseItem.audio && (
            <div className="bg-gradient-to-r from-blue-900/30 to-purple-900/30 rounded-xl p-4 mb-6 shadow-lg border border-white/10">
              <div className="flex flex-col space-y-3">
                <h3 className="text-md font-semibold text-blue-300 flex items-center">
                  <i class="fa-solid fa-headphones mr-2"></i>
                  {caseItem.audio.title || '案例音频'} 
                  {caseItem.audio.duration && (
                    <span className="text-sm text-blue-400 ml-2">({caseItem.audio.duration})</span>
                  )}
                </h3>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <button 
                      onClick={togglePlayPause}
                      className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-2 rounded-full hover:opacity-90 transition-opacity shadow-md"
                    >
                      {isPlaying ? <Pause size={20} /> : <Play size={20} />}
                    </button>
                    <button 
                      onClick={toggleMute}
                      className="bg-white/10 text-gray-300 p-2 rounded-full hover:bg-white/20 transition-colors shadow-sm"
                    >
                      {isMuted ? <VolumeX size={20} /> : <Volume2 size={20} />}
                    </button>
                  </div>
                  <span className="text-sm text-gray-400">
                    {Math.floor(progress)}% 播放进度
                  </span>
                </div>
                
                <div className="w-full bg-gray-700 rounded-full h-1.5">
                  <div 
                    className="bg-gradient-to-r from-blue-500 to-purple-500 h-1.5 rounded-full transition-all duration-300"
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>
                
                <audio
                  ref={audioRef}
                  src={caseItem.audio.url}
                  onTimeUpdate={handleTimeUpdate}
                  onEnded={() => setIsPlaying(false)}
                  className="hidden"
                />
              </div>
            </div>
          )}

          <div className="prose max-w-none text-gray-300">
            {caseItem.content.split('\n').map((paragraph, i) => (
              <p key={i} className="mb-4">{paragraph}</p>
            ))}
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          {caseItem.tags.map(tag => (
            <span key={tag} className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full text-sm">
              {tag}
            </span>
          ))}
        </div>
      </div>

      {playingVideo && (
        <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4">
          <div className="relative w-full max-w-4xl">
            <button 
              onClick={() => setPlayingVideo(null)}
              className="absolute -top-10 right-0 text-white hover:text-gray-300"
            >
              <X size={24} />
            </button>
            <video 
              src={playingVideo} 
              controls 
              autoPlay 
              className="w-full rounded-lg"
            />
          </div>
        </div>
      )}
    </div>
  );
}
