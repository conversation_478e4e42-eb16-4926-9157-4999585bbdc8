# API 接口文档

## 基础信息

- **基础URL**: `/api` (可通过环境变量`VITE_API_BASE_URL`配置)
- **认证方式**: 无 (当前版本)
- **响应格式**: JSON

> 注意：当前项目使用mock数据作为API调用的回退方案。如需连接真实API，请创建.env文件并设置`VITE_API_BASE_URL=您的API地址`

## 案例接口

### 获取案例列表

**请求**
- 方法: `GET`
- 路径: `/cases`

**响应**
```json
[
  {
    "id": 1,
    "title": "小微企业债务重组案例",
    "description": "通过智能运营方案帮助小微企业完成债务重组，恢复经营能力",
    "content": "详细案例内容...",
    "media": [
      {
        "type": "video",
        "url": "https://example.com/videos/case1.mp4",
        "thumbnail": "https://example.com/images/case1.jpg"
      }
    ],
    "tags": ["小微企业", "债务重组"]
  }
]
```

### 获取案例详情

**请求**
- 方法: `GET`
- 路径: `/cases/:id`
- 参数:
  - `id`: 案例ID (路径参数)

**响应**
```json
{
  "id": 1,
  "title": "小微企业债务重组案例",
  "description": "通过智能运营方案帮助小微企业完成债务重组，恢复经营能力",
  "content": "详细案例内容...",
  "media": [
    {
      "type": "video",
      "url": "https://example.com/videos/case1.mp4",
      "thumbnail": "https://example.com/images/case1.jpg"
    }
  ],
  "tags": ["小微企业", "债务重组"]
}
```

## 招聘接口

### 获取职位列表

**请求**
- 方法: `GET`
- 路径: `/careers`

**响应**
```json
[
  {
    "id": 1,
    "title": "AI算法工程师",
    "department": "技术研发部",
    "description": "负责金融债务领域AI模型的研发与优化...",
    "requirements": [
      "计算机、数学或相关专业硕士及以上学历",
      "3年以上机器学习/深度学习相关工作经验"
    ],
    "benefits": [
      "极具竞争力的薪资+股权激励",
      "弹性工作制"
    ],
    "salary": "30k-60k"
  }
]
```

### 提交职位申请

**请求**
- 方法: `POST`
- 路径: `/careers/apply`
- 内容类型: `multipart/form-data`
- 参数:
  - `name`: 姓名 (字符串)
  - `email`: 邮箱 (字符串)
  - `phone`: 电话 (字符串)
  - `positionId`: 职位ID (数字)
  - `resume`: 简历文件 (文件)
  - `coverLetter`: 求职信 (可选字符串)

**响应**
- 成功: HTTP 200 (无内容)
- 失败: HTTP 400/500 (错误信息)

## 新闻接口

### 获取新闻列表

**请求**
- 方法: `GET`
- 路径: `/news`

**响应**
```json
[
  {
    "id": 1,
    "title": "惠他智能完成B轮融资",
    "date": "2025-05-15",
    "summary": "惠他智能宣布完成1.5亿元B轮融资...",
    "content": "本轮融资将主要用于技术研发和市场拓展...",
    "media": [
      {
        "type": "image",
        "url": "https://example.com/images/news1.jpg"
      }
    ]
  }
]
```

### 获取新闻详情

**请求**
- 方法: `GET`
- 路径: `/news/:id`
- 参数:
  - `id`: 新闻ID (路径参数)

**响应**
```json
{
  "id": 1,
  "title": "惠他智能完成B轮融资",
  "date": "2025-05-15",
  "summary": "惠他智能宣布完成1.5亿元B轮融资...",
  "content": "本轮融资将主要用于技术研发和市场拓展...",
  "media": [
    {
      "type": "image",
      "url": "https://example.com/images/news1.jpg"
    }
  ]
}
```

## 错误处理

所有API调用可能返回以下错误格式：

```json
{
  "error": "错误信息",
  "code": "错误代码(可选)"
}
```

常见错误代码：
- 400: 请求参数错误
- 404: 资源不存在
- 500: 服务器内部错误
