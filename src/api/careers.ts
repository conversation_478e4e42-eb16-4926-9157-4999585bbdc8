import api from './index';
import { jobPositions } from '@/data/mock';

export interface JobPosition {
  id: number;
  title: string;
  department: string;
  description: string;
  requirements: string[];
  benefits: string[];
  salary: string;
  qrCode?: string;
}

export interface ApplicationForm {
  name: string;
  email: string;
  phone: string;
  positionId: number;
  resume: File;
  coverLetter?: string;
}

export const getJobPositions = async (): Promise<JobPosition[]> => {
  try {
    const response = await api.get('/careers');
    return response.data;
  } catch (error) {
    console.error('Failed to fetch job positions, using mock data', error);
    return jobPositions;
  }
};

export const submitApplication = async (form: ApplicationForm): Promise<void> => {
  const formData = new FormData();
  Object.entries(form).forEach(([key, value]) => {
    formData.append(key, value);
  });

  try {
    await api.post('/careers/apply', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  } catch (error) {
    console.error('Failed to submit application', error);
    throw error;
  }
};
