import axios from 'axios';

/**
 * API基础配置
 * 基础URL优先级:
 * 1. 环境变量 VITE_API_BASE_URL (需要在项目根目录创建.env文件)
 * 2. 默认值 '/api' (使用mock数据)
 * 
 * 使用示例:
 * import api from '@/api';
 * 
 * 或直接使用模块API:
 * import { getCases } from '@/api/cases';
 * 
 * 注意: 当前项目使用mock数据作为API调用的回退方案。
 * 如需连接真实API，请创建.env文件并设置VITE_API_BASE_URL=您的API地址
 */
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }

});

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 可以在这里添加token等
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response.data;
  },
  error => {
    return Promise.reject(error);
  }
);

export default api;
