// 案例相关API接口
import api from './index';
import { caseStudies } from '@/data/mock';

/**
 * 案例研究接口定义
 * 
 * 使用示例:
 * import { getCases, getCaseDetail } from '@/api/cases';
 * 
 * // 获取案例列表
 * const cases = await getCases();
 * 
 * // 获取案例详情
 * const caseDetail = await getCaseDetail(1);
 */
export interface CaseStudy {
  id: number; // 案例ID
  title: string; // 案例标题
  description: string; // 案例简介
  content: string; // 案例详细内容
  media: { // 媒体资源
    type: 'image' | 'video'; // 媒体类型
    url: string; // 资源URL
    thumbnail?: string; // 视频缩略图(可选)
  }[];
  audio?: { // 音频资源
    url: string; // 音频URL
    title?: string; // 音频标题
    duration?: string; // 音频时长
  };
  tags: string[]; // 案例标签
}

/**
 * 获取案例列表
 * @returns 案例列表Promise
 */
export const getCases = async (): Promise<CaseStudy[]> => {
  try {
    // 调用API获取案例数据
    const response = await api.get('/cases');
    if (response.data && response.data.length > 0) {
      return response.data;
    }
    return caseStudies;
  } catch (error) {
    console.error('Failed to fetch cases, using mock data', error);
    return caseStudies;
  }
};

/**
 * 获取案例详情
 * @param id 案例ID
 * @returns 案例详情Promise
 */
export const getCaseDetail = async (id: number): Promise<CaseStudy> => {
  try {
    // 调用API获取指定案例详情
    const response = await api.get(`/cases/${id}`);
    if (response.data) {
      return response.data;
    }
    return caseStudies.find(c => c.id === id) || caseStudies[0];
  } catch (error) {
    console.error(`Failed to fetch case ${id}, using mock data`, error);
    return caseStudies.find(c => c.id === id) || caseStudies[0];
  }
};
