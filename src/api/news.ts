import api from './index';
import { newsData } from '@/data/mock';

export interface NewsItem {
  id: number;
  title: string;
  date: string;
  summary: string;
  content: string;
  media: {
    type: 'image' | 'video';
    url: string;
    thumbnail?: string;
  }[];
}

export const getNewsList = async (): Promise<NewsItem[]> => {
  try {
    const response = await api.get('/news');
    return response.data;
  } catch (error) {
    console.error('Failed to fetch news, using mock data', error);
    return newsData;
  }
};

export const getNewsDetail = async (id: number): Promise<NewsItem> => {
  try {
    const response = await api.get(`/news/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Failed to fetch news ${id}, using mock data`, error);
    return newsData.find(n => n.id === id) || newsData[0];
  }
};
