import { createContext, useContext } from "react";
import { Routes, Route } from "react-router-dom";
import QRCodeDrawer from "@/components/QRCodeDrawer";
import Home from "@/pages/Home";
import Comparison from "@/pages/Comparison";
import Cases from "@/pages/Cases";
import CaseDetail from "@/pages/CaseDetail";
import Partners from "@/pages/Partners";
import Careers from "@/pages/Careers";
import About from "@/pages/About";
import Privacy from "@/pages/Privacy";
import AICallCenter from "@/pages/AICallCenter";
import News from "@/pages/News";
import Navbar from "@/components/Navbar";

interface AuthContextType {
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType>({
  isAuthenticated: false,
});

export { AuthContext };

export default function App() {
  // 系统设置 - 临时屏蔽某些页面
  const systemSettings = {
    showNewsPage: false,
    showCareersPage: false
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated: false }}>
      <Navbar />
      <Routes>
        <Route path="/" element={<Home />} exact />
        <Route path="/compare" element={<Comparison />}>
          <Route path="ai-call-center" element={<AICallCenter />} />
        </Route>
        <Route path="/cases" element={<Cases />} />
        <Route path="/cases/:id" element={<CaseDetail />} />
        <Route path="/partners" element={<Partners />} exact />
        {systemSettings.showCareersPage && (
          <Route path="/careers" element={<Careers />} exact />
        )}
        <Route path="/about" element={<About />} exact />
        {systemSettings.showNewsPage && (
          <Route path="/news" element={<News />} exact />
        )}
        <Route path="/privacy" element={<Privacy />} />
      </Routes>
      <QRCodeDrawer />
    </AuthContext.Provider>
  );
}