import { Suspense, lazy } from "react";
import { Routes, Route } from "react-router-dom";
import QRCodeDrawer from "@/components/QRCodeDrawer";
import Navbar from "@/components/Navbar";
import LoadingSpinner from "@/components/LoadingSpinner";
import { AuthContext } from "@/contexts/AuthContext";

// 懒加载页面组件
const Home = lazy(() => import("@/pages/Home"));
const Comparison = lazy(() => import("@/pages/Comparison"));
const Cases = lazy(() => import("@/pages/Cases"));
const CaseDetail = lazy(() => import("@/pages/CaseDetail"));
const Partners = lazy(() => import("@/pages/Partners"));
const Careers = lazy(() => import("@/pages/Careers"));
const About = lazy(() => import("@/pages/About"));
const Privacy = lazy(() => import("@/pages/Privacy"));
const AICallCenter = lazy(() => import("@/pages/AICallCenter"));
const News = lazy(() => import("@/pages/News"));



export default function App() {
  // 系统设置 - 临时屏蔽某些页面
  const systemSettings = {
    showNewsPage: false,
    showCareersPage: false
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated: false }}>
      <Navbar />
      <Suspense fallback={<LoadingSpinner />}>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/compare" element={<Comparison />}>
            <Route path="ai-call-center" element={<AICallCenter />} />
          </Route>
          <Route path="/cases" element={<Cases />} />
          <Route path="/cases/:id" element={<CaseDetail />} />
          <Route path="/partners" element={<Partners />} />
          {systemSettings.showCareersPage && (
            <Route path="/careers" element={<Careers />} />
          )}
          <Route path="/about" element={<About />} />
          {systemSettings.showNewsPage && (
            <Route path="/news" element={<News />} />
          )}
          <Route path="/privacy" element={<Privacy />} />
          {/* 添加404页面 */}
          <Route path="*" element={
            <div className="min-h-screen flex items-center justify-center bg-gray-900">
              <div className="text-center">
                <h1 className="text-4xl font-bold text-white mb-4">404</h1>
                <p className="text-gray-300 mb-8">页面未找到</p>
                <a href="/" className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                  返回首页
                </a>
              </div>
            </div>
          } />
        </Routes>
      </Suspense>
      <QRCodeDrawer />
    </AuthContext.Provider>
  );
}