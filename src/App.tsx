import { createContext, Suspense, lazy } from "react";
import { Routes, Route } from "react-router-dom";
import QRCodeDrawer from "@/components/QRCodeDrawer";
import Navbar from "@/components/Navbar";
import LoadingSpinner from "@/components/LoadingSpinner";

// 懒加载页面组件
const Home = lazy(() => import("@/pages/Home"));
const Comparison = lazy(() => import("@/pages/Comparison"));
const Cases = lazy(() => import("@/pages/Cases"));
const CaseDetail = lazy(() => import("@/pages/CaseDetail"));
const Partners = lazy(() => import("@/pages/Partners"));
const Careers = lazy(() => import("@/pages/Careers"));
const About = lazy(() => import("@/pages/About"));
const Privacy = lazy(() => import("@/pages/Privacy"));
const AICallCenter = lazy(() => import("@/pages/AICallCenter"));
const News = lazy(() => import("@/pages/News"));

interface AuthContextType {
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType>({
  isAuthenticated: false,
});

export { AuthContext };

export default function App() {
  // 系统设置 - 临时屏蔽某些页面
  const systemSettings = {
    showNewsPage: false,
    showCareersPage: false
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated: false }}>
      <Navbar />
      <Suspense fallback={<LoadingSpinner />}>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/compare" element={<Comparison />}>
            <Route path="ai-call-center" element={<AICallCenter />} />
          </Route>
          <Route path="/cases" element={<Cases />} />
          <Route path="/cases/:id" element={<CaseDetail />} />
          <Route path="/partners" element={<Partners />} />
          {systemSettings.showCareersPage && (
            <Route path="/careers" element={<Careers />} />
          )}
          <Route path="/about" element={<About />} />
          {systemSettings.showNewsPage && (
            <Route path="/news" element={<News />} />
          )}
          <Route path="/privacy" element={<Privacy />} />
        </Routes>
      </Suspense>
      <QRCodeDrawer />
    </AuthContext.Provider>
  );
}