@tailwind base;
@tailwind components;
@tailwind utilities;

/* 修复移动端图表溢出问题 */
.recharts-wrapper {
  overflow: visible !important;
}

/* 合作伙伴卡片响应式调整 */
@media (max-width: 640px) {
  .min-w-\[150px\] {
    min-width: 150px;
  }
}

:root {
  font-family: 'Raj<PERSON><PERSON>', 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
  line-height: 1.5;
  font-weight: 400;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@import url('https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&family=Inter:wght@400;500;600;700&display=swap');

/* 霓虹灯效果 - 优化版 */
.neon-effect {
  text-shadow: 0 0 3px rgba(255,255,255,0.7), 0 0 6px rgba(0,115,230,0.5);
  animation: neon-pulse 2s infinite alternate;
}

/* 卡片悬浮效果 */
.card-hover-3d {
  transform-style: preserve-3d;
  transition: transform 0.5s ease, box-shadow 0.5s ease;
}

.card-hover-3d:hover {
  transform: perspective(1000px) rotateX(5deg) rotateY(5deg) translateY(-10px);
  box-shadow: 0 20px 50px rgba(59, 130, 246, 0.3);
}

/* 内容展开动画 */
.content-expand {
  transition: max-height 0.5s ease, opacity 0.3s ease;
}

@keyframes neon-pulse {
  from {
    text-shadow: 0 0 3px rgba(255,255,255,0.7), 0 0 6px rgba(0,115,230,0.5);
  }
  to {
    text-shadow: 0 0 5px rgba(255,255,255,0.9), 0 0 10px rgba(0,115,230,0.7);
  }
}

/* 文字发光效果 */
@keyframes text-glow {
  0%, 100% {
    text-shadow: 0 0 10px rgba(59, 130, 246, 0.8);
  }
  50% {
    text-shadow: 0 0 20px rgba(124, 58, 237, 0.8);
  }
}

.animate-text-glow {
  animation: text-glow 3s ease-in-out infinite alternate;
}

/* 渐变流动效果 */
@keyframes gradient-flow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient-flow {
  background: linear-gradient(-45deg, #1e40af, #7c3aed, #1e40af);
  background-size: 400% 400%;
  animation: gradient-flow 15s ease infinite;
}

/* 网格背景效果 */
.grid-pattern {
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 40px 40px;
}

/* 卡片悬停效果 */
.card-hover-effect {
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-hover-effect:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 边框发光效果 */
.glow-border {
  position: relative;
}

.glow-border::after {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 1px;
  background: linear-gradient(45deg, #3b82f6, #8b5cf6);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
}