// 优化的动画配置
export const animationConfig = {
  // 基础动画变体
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: { duration: 0.3, ease: "easeOut" }
  },

  fadeInUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
    transition: { duration: 0.4, ease: [0.16, 1, 0.3, 1] }
  },

  fadeInScale: {
    initial: { opacity: 0, scale: 0.95 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.95 },
    transition: { duration: 0.3, ease: "easeOut" }
  },

  // 悬停动画（使用transform优化性能）
  hoverLift: {
    whileHover: { 
      y: -8,
      transition: { type: "spring", stiffness: 400, damping: 10 }
    }
  },

  hoverScale: {
    whileHover: { 
      scale: 1.02,
      transition: { type: "spring", stiffness: 400, damping: 10 }
    }
  },

  // 点击动画
  tapScale: {
    whileTap: { scale: 0.98 }
  },

  // 视口动画（优化版本）
  inView: {
    initial: { opacity: 0, y: 30 },
    whileInView: { opacity: 1, y: 0 },
    viewport: { once: true, margin: "-50px" },
    transition: { duration: 0.5, ease: "easeOut" }
  },

  // 交错动画
  stagger: {
    animate: {
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  },

  staggerItem: {
    initial: { opacity: 0, y: 20 },
    animate: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.4, ease: [0.16, 1, 0.3, 1] }
    }
  }
};

// 性能优化的动画属性
export const performantAnimationProps = {
  // 只使用transform和opacity属性以避免重排
  layoutId: undefined,
  // 启用硬件加速
  style: { willChange: 'transform, opacity' },
  // 减少动画复杂度
  transition: { type: "tween", ease: "easeOut" }
};

// 减少动画的媒体查询检测
export const shouldReduceMotion = () => {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

// 条件动画包装器
export const conditionalAnimation = (animation: any) => {
  return shouldReduceMotion() ? {} : animation;
};
