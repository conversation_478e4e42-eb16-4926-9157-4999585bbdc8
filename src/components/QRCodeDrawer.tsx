import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, QrCode } from 'lucide-react';

export default function QRCodeDrawer() {
  const [isOpen, setIsOpen] = useState(false);

  const toggleDrawer = () => {
    setIsOpen(!isOpen);
  };

  return (
    <>
      {/* 悬浮按钮 */}
      <motion.button
        onClick={toggleDrawer}
        className="fixed bottom-8 right-8 z-50 bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-full shadow-xl hover:shadow-2xl transition-all"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
      >
        <QrCode size={24} />
      </motion.button>

      {/* 弹出框 */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 100 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 100 }}
            transition={{ type: 'spring', damping: 25 }}
            className="fixed bottom-24 right-8 z-50 bg-white rounded-xl shadow-2xl p-6 w-64"
          >
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-semibold text-gray-800">扫码关注我们</h3>
              <button 
                onClick={toggleDrawer}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={20} />
              </button>
            </div>
            <img 
              src="https://space.coze.cn/api/coze_space/gen_image?image_size=square&prompt=company%20qr%20code&sign=ab707199a8e7ffeca9ae751cbced7c5a"
              alt="微信公众号二维码"
              className="w-full h-auto"
            />
            <p className="text-sm text-gray-500 mt-2 text-center">扫描二维码关注公众号</p>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
