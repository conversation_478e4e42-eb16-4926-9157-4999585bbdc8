// 导航栏组件 - 提供网站主要导航功能
import { Link, useLocation } from 'react-router-dom';
import { useState } from 'react';
import { Menu, X } from 'lucide-react';

/**
 * 网站导航栏组件
 * 包含桌面和移动端导航菜单
 */
export default function Navbar() {
  // 当前路由信息
  const location = useLocation();
  // 移动端菜单展开状态
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  /**
   * 检查当前路由是否激活
   * @param path 要检查的路径
   * @returns 是否激活
   */
  const isActive = (path: string) => {
    if (path === '/') {
      return location.pathname === path;
    }
    return location.pathname.startsWith(path);
  };

  const navLinks = [
    { path: '/', name: '首页' },
    { path: '/compare', name: '产品与服务' },
    { path: '/cases', name: '案例中心' },
    { path: '/about', name: '关于我们' }
    // 暂时隐藏公司动态和加入我们链接
    // { path: '/news', name: '公司动态' },
    // { path: '/careers', name: '加入我们' }
  ];

  return (
    <nav className="bg-gradient-to-r from-blue-900/50 to-purple-900/50 backdrop-blur-md shadow-sm">
      <div className="container mx-auto px-4 py-4 flex items-center justify-between">
        {/* Logo - 左侧 */}
        <Link to="/" className="flex items-center">
          <div className="h-12 w-auto flex items-center justify-center overflow-hidden">
            <img 
              src="https://huitaweb-prd.oss-cn-shenzhen.aliyuncs.com/website/huita-web-log.png"
              alt="公司logo"
              className="h-full object-contain transition-all duration-300 hover:opacity-90"
            />
          </div>
        </Link>

        {/* 桌面导航 - 右侧 */}
        <div className="hidden md:flex space-x-6">
          {navLinks.map((link) => (
            <Link 
              key={link.path}
              to={link.path} 
                className={`px-3 py-2 rounded-md text-base font-medium transition-all duration-300 ${
                 isActive(link.path) ? 'bg-blue-500/20 text-white ring-1 ring-blue-400/20' : 'text-white/90 hover:text-white hover:bg-blue-500/10'
               }`}
            >
              {link.name}
            </Link>
          ))}
        </div>

        {/* 移动端菜单按钮 */}
        <button 
          className="md:hidden text-white focus:outline-none"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        >
          {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>

      {/* 移动端下拉菜单 */}
      {mobileMenuOpen && (
        <div className="md:hidden bg-blue-900/90 backdrop-blur-md">
          <div className="px-4 pt-4 pb-6 space-y-3">
            {navLinks.map((link) => (
              <Link
                key={link.path}
                to={link.path}
                className={`flex items-center px-4 py-3 rounded-lg text-lg font-medium ${
                  isActive(link.path) 
                    ? 'bg-blue-500/20 text-white ring-1 ring-blue-400/20' 
                    : 'text-white/90 hover:text-white hover:bg-blue-500/10'
                }`}
                onClick={() => setMobileMenuOpen(false)}
              >
                <i class={`fa-solid ${
                  link.path === '/' ? 'fa-home' :
                  link.path === '/compare' ? 'fa-chart-bar' :
                  link.path === '/cases' ? 'fa-folder-open' :
                  link.path === '/about' ? 'fa-info-circle' :
                  'fa-briefcase'
                } mr-3 text-lg`}></i>
                {link.name}
              </Link>
            ))}
          </div>
        </div>
      )}
    </nav>
  );
}