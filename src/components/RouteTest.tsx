import { Link } from 'react-router-dom';

const RouteTest = () => {
  const routes = [
    { path: '/', name: '首页' },
    { path: '/about', name: '关于我们' },
    { path: '/cases', name: '案例中心' },
    { path: '/partners', name: '合作伙伴' },
    { path: '/compare', name: '产品对比' },
    { path: '/privacy', name: '隐私政策' }
  ];

  return (
    <div className="fixed top-4 right-4 z-50 bg-black/80 text-white p-4 rounded-lg">
      <h3 className="text-sm font-bold mb-2">路由测试</h3>
      <div className="space-y-1">
        {routes.map(route => (
          <Link
            key={route.path}
            to={route.path}
            className="block text-xs hover:text-blue-300 transition-colors"
          >
            {route.name}
          </Link>
        ))}
      </div>
    </div>
  );
};

export default RouteTest;
