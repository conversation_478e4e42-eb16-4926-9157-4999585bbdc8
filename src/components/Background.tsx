import { useEffect, useRef, memo, useCallback } from 'react';

const Background = memo(() => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationIdRef = useRef<number>();
  const particlesRef = useRef<Particle[]>([]);

  useEffect(() => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const updateCanvasSize = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    updateCanvasSize();

    // 优化粒子数量，根据设备性能调整
    const getParticleCount = () => {
      const baseCount = Math.floor(window.innerWidth / 20); // 减少粒子数量
      const maxCount = 50; // 设置最大粒子数
      return Math.min(baseCount, maxCount);
    };

    const particleCount = getParticleCount();

    class Particle {
      x: number;
      y: number;
      size: number;
      speedX: number;
      speedY: number;
      color: string;
      alpha: number;

      constructor() {
        this.x = Math.random() * canvas.width;
        this.y = Math.random() * canvas.height;
        this.size = Math.random() * 1.5 + 0.5; // 减小粒子大小
        this.speedX = (Math.random() - 0.5) * 1; // 减慢速度
        this.speedY = (Math.random() - 0.5) * 1;
        this.alpha = Math.random() * 0.5 + 0.2; // 添加透明度变化
        this.color = `rgba(${Math.floor(Math.random() * 50 + 150)}, ${Math.floor(
          Math.random() * 50 + 150
        )}, ${Math.floor(Math.random() * 50 + 200)}, ${this.alpha})`;
      }

      update() {
        this.x += this.speedX;
        this.y += this.speedY;

        // 边界检测优化
        if (this.x < 0 || this.x > canvas.width) {
          this.speedX *= -1;
          this.x = Math.max(0, Math.min(canvas.width, this.x));
        }
        if (this.y < 0 || this.y > canvas.height) {
          this.speedY *= -1;
          this.y = Math.max(0, Math.min(canvas.height, this.y));
        }
      }

      draw() {
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fillStyle = this.color;
        ctx.fill();
      }
    }

    // 初始化粒子
    particlesRef.current = [];
    for (let i = 0; i < particleCount; i++) {
      particlesRef.current.push(new Particle());
    }

    // 优化的动画循环
    let lastTime = 0;
    const targetFPS = 30; // 降低帧率以提高性能
    const frameInterval = 1000 / targetFPS;

    const animate = (currentTime: number) => {
      if (currentTime - lastTime >= frameInterval) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // 绘制渐变背景（缓存渐变）
        const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
        gradient.addColorStop(0, 'rgba(26, 32, 44, 0.8)');
        gradient.addColorStop(1, 'rgba(49, 46, 129, 0.6)');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 批量更新和绘制粒子
        const particles = particlesRef.current;
        for (let i = 0; i < particles.length; i++) {
          particles[i].update();
          particles[i].draw();
        }

        lastTime = currentTime;
      }

      animationIdRef.current = requestAnimationFrame(animate);
    };

    animationIdRef.current = requestAnimationFrame(animate);

    // 防抖的resize处理
    const handleResize = useCallback(() => {
      updateCanvasSize();
      // 重新初始化粒子
      const newParticleCount = getParticleCount();
      particlesRef.current = [];
      for (let i = 0; i < newParticleCount; i++) {
        particlesRef.current.push(new Particle());
      }
    }, []);

    let resizeTimeout: NodeJS.Timeout;
    const debouncedResize = () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(handleResize, 250);
    };

    window.addEventListener('resize', debouncedResize);

    return () => {
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
      }
      window.removeEventListener('resize', debouncedResize);
      clearTimeout(resizeTimeout);
    };
  }, []);

  return (
    <div className="fixed inset-0 -z-10 overflow-hidden">
      <canvas
        ref={canvasRef}
        className="absolute inset-0 w-full h-full"
      />
      <div className="absolute inset-0 bg-[url('https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=tech%20background%20with%20particles%20and%20circuits&sign=f1a84699569b7cacb01857badaee5564')] opacity-10 bg-cover bg-center" />
    </div>
  );
});

Background.displayName = 'Background';

export default Background;