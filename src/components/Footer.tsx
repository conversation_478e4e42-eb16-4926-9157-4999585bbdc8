import { Link } from 'react-router-dom';

export default function Footer() {
  return (
    <footer className="bg-gradient-to-r from-blue-900/80 to-purple-900/80 backdrop-blur-sm py-8 px-4">
      <div className="container mx-auto">
        <div className="flex flex-col items-center md:flex-row md:justify-between md:items-center space-y-4 md:space-y-0">
          <div className="text-center md:text-left">
            <h3 className="text-xl font-semibold text-white">惠他智能科技有限公司</h3>
            <p className="text-blue-100 mt-2">佛山顺德区美的置业写字楼1903、1904号</p>
          </div>
          <div className="flex flex-wrap justify-center gap-4 md:gap-6">
            <Link to="/about" className="text-blue-200 hover:text-white transition-colors px-2 py-1 text-sm md:text-base">关于我们</Link>
            <Link to="/privacy" className="text-blue-200 hover:text-white transition-colors px-2 py-1 text-sm md:text-base">隐私政策</Link>
          </div>
        </div>
        <div className="mt-8 pt-6 border-t border-blue-700/50 text-center text-blue-100 text-sm md:text-base">
          <p>© {new Date().getFullYear()} 广州惠他智能科技有限公司 版权所有</p>
          <div className="flex justify-center mt-2 text-xs text-blue-200/80">
            <a href="https://beian.miit.gov.cn" target="_blank" rel="noopener noreferrer">ICP备案号：粤ICP备2025430829号</a>
          </div>
        </div>
      </div>
    </footer>
  );
}