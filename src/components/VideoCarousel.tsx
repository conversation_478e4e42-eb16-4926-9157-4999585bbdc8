import { useState, useEffect } from 'react';
import { Play, Pause, ChevronLeft, ChevronRight, Subtitles, Image, Video } from 'lucide-react';
import { motion } from 'framer-motion';


interface MediaData {
  id: number;
  title: string;
  type: 'video' | 'image';
  url: string;
  subtitleUrl?: string;
  thumbnail: string;
}

interface VideoCarouselProps {
  videos: MediaData[];
}

export default function VideoCarousel({ videos }: VideoCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);
  const [showSubtitles, setShowSubtitles] = useState(false);
  const [mediaType, setMediaType] = useState<'video' | 'image'>('video');

  useEffect(() => {
    let interval: ReturnType<typeof setTimeout>;
    
    if (isPlaying && videos.length > 1) {
      interval = setInterval(() => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % videos.length);
      }, 8000);
    }

    return () => clearInterval(interval);
  }, [isPlaying, videos.length]);

  useEffect(() => {
    setMediaType(videos[currentIndex]?.type || 'video');
  }, [currentIndex, videos]);

  const goToPrev = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? videos.length - 1 : prevIndex - 1
    );
  };

  const goToNext = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === videos.length - 1 ? 0 : prevIndex + 1
    );
  };

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const toggleSubtitles = () => {
    setShowSubtitles(!showSubtitles);
  };

  const toggleMediaType = () => {
    setMediaType(mediaType === 'video' ? 'image' : 'video');
  };

  return (
    <div className="relative w-full h-[600px] overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900/30 via-purple-900/20 to-transparent backdrop-blur-sm"></div>
      
      <div className="relative h-full flex items-center justify-center">
        {mediaType === 'video' ? (
          <video
            key={videos[currentIndex].id}
            className="w-full h-full object-cover"
            autoPlay={isPlaying}
            muted
            loop
          >
            <source src={videos[currentIndex].url} type="video/mp4" />
          </video>
        ) : (
          <img
            src={videos[currentIndex].thumbnail}
            alt={videos[currentIndex].title}
            className="w-full h-full object-cover"
          />
        )}
        
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex items-center space-x-4 bg-white/10 backdrop-blur-md rounded-full px-4 py-2 border border-white/20">
          <button 
            onClick={goToPrev}
            className="text-white hover:text-blue-300 transition-colors"
          >
            <ChevronLeft size={24} />
          </button>
          
           <motion.button 
             onClick={togglePlayPause}
             whileTap={{ scale: 0.9 }}
             className="text-white hover:text-blue-300 transition-colors"
             transition={{ type: "spring", stiffness: 400, damping: 10 }}
           >
             {isPlaying ? <Pause size={24} /> : <Play size={24} />}
           </motion.button>
          
          <button 
            onClick={goToNext}
            className="text-white hover:text-blue-300 transition-colors"
          >
            <ChevronRight size={24} />
          </button>
          
          <button 
            onClick={toggleSubtitles}
            className={`ml-4 ${showSubtitles ? 'text-green-400' : 'text-white/80'} hover:text-green-300 transition-colors`}
          >
            <Subtitles size={24} />
          </button>

          <button 
            onClick={toggleMediaType}
            className={`ml-4 ${mediaType === 'image' ? 'text-purple-400' : 'text-white/80'} hover:text-purple-300 transition-colors`}
          >
            {mediaType === 'video' ? <Image size={24} /> : <Video size={24} />}
          </button>
        </div>
      </div>
    </div>
  );
}