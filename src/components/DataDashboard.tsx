import { motion } from 'framer-motion';

interface DashboardData {
  totalAmount: number;
  successRate: number;
  monthlyData: {
    month: string;
    amount: number;
    successRate: number;
  }[];
}

interface DataDashboardProps {
  data: DashboardData;
}

export default function DataDashboard({ data }: DataDashboardProps) {
  return (
    <div className="p-8 flex justify-center">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-2xl">
        <motion.div 
          whileHover={{ scale: 1.02 }}
          className="bg-gradient-to-br from-blue-900/30 to-purple-900/30 backdrop-blur-sm p-8 rounded-xl shadow-lg border border-white/10"
        >
          <h3 className="text-xl font-medium text-blue-200 mb-4">累计处置金额</h3>
          <p className="text-4xl font-bold text-white">
            ¥{(data.totalAmount / 100000000).toFixed(2)}亿
          </p>
        </motion.div>
        
        <motion.div 
          whileHover={{ scale: 1.02 }}
          className="bg-gradient-to-br from-green-900/30 to-blue-900/30 backdrop-blur-sm p-8 rounded-xl shadow-lg border border-white/10"
        >
          <h3 className="text-xl font-medium text-green-200 mb-4">平均成功率</h3>
          <p className="text-4xl font-bold text-white">
            {data.successRate}%
          </p>
        </motion.div>
      </div>
    </div>
  );
}