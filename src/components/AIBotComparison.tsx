import { useState } from 'react';
import { Radar<PERSON>hart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, ResponsiveContainer } from 'recharts';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';

interface ComparisonData {
  technical: {
    title: string;
    description: string;
    chartData: {
      subject: string;
      A: number;
      B: number;
    }[];
  };
  social: {
    title: string;
    timeline: {
      step: number;
      title: string;
      duration: string;
    }[];
  };
  financial: {
    title: string;
    radarData: {
      subject: string;
      traditional: number;
      smart: number;
    }[];
  };
}

interface AIBotComparisonProps {
  data: ComparisonData;
}

export default function AIBotComparison({ data }: AIBotComparisonProps) {
  const [activeFace, setActiveFace] = useState('technical');

  const renderFaceContent = () => {
    switch (activeFace) {
      case 'technical':
          return (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ 
                duration: 0.3,
                ease: [0.16, 1, 0.3, 1],
                opacity: { duration: 0.2 }
              }}
              className="p-6"
            >
              <h3 className="text-xl font-semibold mb-4 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-blue-600">
                {data.technical.title}
              </h3>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <RadarChart cx="50%" cy="50%" outerRadius="80%" data={data.technical.chartData}>
                    <PolarGrid stroke="#4b5563" />
                    <PolarAngleAxis dataKey="subject" stroke="#e5e7eb" />
                    <PolarRadiusAxis angle={30} domain={[0, 100]} stroke="#e5e7eb" />
                    <Radar 
                      name="传统" 
                      dataKey="A" 
                      stroke="#8884d8" 
                      fill="#8884d8" 
                      fillOpacity={0.6} 
                      animationDuration={800}
                      animationEasing="ease-out"
                    />
                    <Radar 
                      name="智能" 
                      dataKey="B" 
                      stroke="#82ca9d" 
                      fill="#82ca9d" 
                      fillOpacity={0.6}
                      animationDuration={800}
                      animationEasing="ease-out"
                    />
                  </RadarChart>
                </ResponsiveContainer>
              </div>
              <p className="text-gray-300 mt-4">{data.technical.description}</p>
            </motion.div>
          );
      case 'social':
        return (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="p-6"
          >
            <h3 className="text-xl font-semibold mb-4 text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-green-600">
              {data.social.title}
            </h3>
            <div className="space-y-4">
              {data.social.timeline.map((item) => (
                <motion.div 
                  key={item.step}
                  whileHover={{ scale: 1.02 }}
                  className="flex items-start bg-gray-800/50 backdrop-blur-sm p-4 rounded-lg border border-gray-700/50"
                >
                  <div className="bg-green-500/20 text-green-400 rounded-full w-8 h-8 flex items-center justify-center mr-4 shadow-lg">
                    {item.step}
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-200">{item.title}</h4>
                    <p className="text-sm text-gray-400">{item.duration}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        );
      case 'financial':
        return (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="p-6"
          >
            <h3 className="text-xl font-semibold mb-4 text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-purple-600">
              {data.financial.title}
            </h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <RadarChart cx="50%" cy="50%" outerRadius="80%" data={data.financial.radarData}>
                  <PolarGrid stroke="#4b5563" />
                  <PolarAngleAxis dataKey="subject" stroke="#e5e7eb" />
                  <PolarRadiusAxis angle={30} domain={[0, 100]} stroke="#e5e7eb" />
                  <Radar 
                    name="传统催收" 
                    dataKey="traditional" 
                    stroke="#8884d8" 
                    fill="#8884d8" 
                    fillOpacity={0.6}
                    animationDuration={1500}
                    animationEasing="ease-out"
                  />
                  <Radar 
                    name="智能运营" 
                    dataKey="smart" 
                    stroke="#82ca9d" 
                    fill="#82ca9d" 
                    fillOpacity={0.6}
                    animationDuration={1500}
                    animationEasing="ease-out"
                  />
                </RadarChart>
              </ResponsiveContainer>
            </div>
          </motion.div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="bg-gradient-to-br from-blue-900/20 to-purple-900/20 backdrop-blur-sm rounded-2xl shadow-xl overflow-hidden border border-white/10">
      <div className="flex flex-col md:flex-row">
        <div className="md:w-1/2 p-6 flex items-center justify-center bg-gradient-to-br from-blue-900/30 to-purple-900/30">
          <div className="w-full h-64 md:h-96 relative flex items-center justify-center">
            <img 
              src="https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=futuristic%20AI%20robot%20with%20holographic%20interface%20and%20glowing%20circuits&sign=61c7cd8ab6fe0535a2f236d4c4443e5d"
              alt="AI Robot"
              className="w-full h-full object-contain"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent flex items-end p-6">
              <h2 className="text-2xl font-bold text-white">智能AI机器人</h2>
            </div>
          </div>
        </div>
        <div className="md:w-1/2 bg-gray-900/70 backdrop-blur-sm">
          <AnimatePresence mode="wait">
            {renderFaceContent()}
          </AnimatePresence>
        </div>
      </div>
      <div className="bg-gradient-to-r from-blue-900/30 via-purple-900/30 to-blue-900/30 p-4 flex justify-center space-x-4 border-t border-white/10">
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => setActiveFace('technical')}
          className={cn(
            'px-6 py-3 rounded-xl transition-all flex items-center',
            activeFace === 'technical' 
              ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg ring-2 ring-blue-400/50' 
              : 'bg-gray-800/80 text-gray-300 hover:bg-gray-700/80'
          )}
        >
          <i class="fa-solid fa-microchip mr-2 text-lg"></i>
          <span className="font-medium">技术维度</span>
        </motion.button>
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => setActiveFace('social')}
          className={cn(
            'px-6 py-3 rounded-xl transition-all flex items-center',
            activeFace === 'social' 
              ? 'bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg ring-2 ring-green-400/50' 
              : 'bg-gray-800/80 text-gray-300 hover:bg-gray-700/80'
          )}
        >
          <i class="fa-solid fa-users mr-2 text-lg"></i>
          <span className="font-medium">社会维度</span>
        </motion.button>
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => setActiveFace('financial')}
          className={cn(
            'px-6 py-3 rounded-xl transition-all flex items-center',
            activeFace === 'financial' 
              ? 'bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-lg ring-2 ring-purple-400/50' 
              : 'bg-gray-800/80 text-gray-300 hover:bg-gray-700/80'
          )}
        >
          <i class="fa-solid fa-chart-line mr-2 text-lg"></i>
          <span className="font-medium">财务维度</span>
        </motion.button>
      </div>
    </div>
  );
}