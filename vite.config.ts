/** WARNING: DON'T EDIT THIS FILE */
/** WARNING: DON'T EDIT THIS FILE */
/** WARNING: DON'T EDIT THIS FILE */

import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tsconfigPaths from "vite-tsconfig-paths";

function getPlugins() {
  const plugins = [react(), tsconfigPaths()];
  return plugins;
}

export default defineConfig({
  plugins: getPlugins(),
  build: {
    // 启用代码分割
    rollupOptions: {
      output: {
        manualChunks: {
          // 将React相关库分离到单独的chunk
          react: ['react', 'react-dom'],
          // 将路由相关库分离
          router: ['react-router-dom'],
          // 将动画库分离
          animation: ['framer-motion'],
          // 将图标库分离
          icons: ['lucide-react'],
          // 将图表库分离
          charts: ['recharts'],
          // 将工具库分离
          utils: ['clsx', 'tailwind-merge', 'zod']
        }
      }
    },
    // 启用压缩
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // 移除console.log
        drop_debugger: true, // 移除debugger
        pure_funcs: ['console.log', 'console.info', 'console.debug'] // 移除特定函数调用
      }
    },
    // 设置chunk大小警告限制
    chunkSizeWarningLimit: 1000,
    // 启用CSS代码分割
    cssCodeSplit: true,
    // 生成source map（生产环境可以设为false）
    sourcemap: false
  },
  // 优化依赖预构建
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'framer-motion',
      'lucide-react'
    ]
  },
  // 服务器配置
  server: {
    // 启用gzip压缩
    compress: true
  }
});
